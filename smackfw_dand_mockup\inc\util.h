#ifndef _UTIL_H_
#define _UTIL_H_

#include <stdlib.h>

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include "core_cm0.h"

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "system_lib.h"
#include "sys_tick_lib.h"
#include "sys_tim_lib.h"
#include "shc_lib.h"

// Delay function based on SysTick
// The systick_singleshot_lib() function is using the WFI function during the delay to save power
void delay_ticks(uint32_t ticks);

// get random uint32_t number
uint32_t get_random_u32(uint32_t offset);

#endif
