# SMACK Device Commands Reference

## Command Format

Each command consists of multiple 32-bit words in big-endian format:

1. Command Header (32 bits)
   ```
   0x60000000 - Fixed header value
   ```

2. Length and Flags (32 bits)
   ```
   Format: 0xLLSSFFFF
   LL: Total length (high 16 bits)
   SS: Sub-command flags (middle 8 bits)
   FFFF: Command flags (low 16 bits)
   
   Flags:
   - 0x0001: Normal command
   - 0x8001: Encrypted command
   ```

3. Reserved (32 bits)
   ```
   0x00000000 - Reserved for future use
   ```

4. Command ID (32 bits)
   ```
   Format: 0xTTTT0000 | XXXX
   TTTT: Command type
   XXXX: Data point ID
   ```

## System Information Commands

### Read Device Flags
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x07000000 | X0000_SMACK_FLAGS]
Purpose: Get the device status flags
Response: Status flags including device initialization state
Flags:
    - smack_flags_error   = (1U << 31)  // Error state
    - smack_flags_locked  = (1U << 1)   // Device is locked
    - smack_flags_unlocked = (1U << 0)  // Device is unlocked
Note: When NVM is erased (new device), MESSAGE_AND_CALL_ENABLE will be 0xffffffff
```

### Read Firmware Version
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x07000000 | X0001_FW_VERSION]
Purpose: Get the firmware version of the device
```

### Read Platform Info
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x07000000 | X0002_FW_PLATFORM]
Purpose: Get the platform information
```

### Read Device UID
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x09000000 | X0004_UID]
Purpose: Get the unique identifier of the device
```

### Read Lock ID
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x09000000 | X0005_LOCK_ID]
Purpose: Get the lock identifier
```

## Lock Control Commands

### Arm Lock
```
Command: [0x60000000, 0x000c0003, 0x00000000, 0x83010000 | X0121_LOCK_ARM, 0x02000000]
Purpose: Prepare the lock for operation
```

### Execute Lock Control
```
Command: [0x60000000, 0x000c0003, 0x00000000, 0x83010000 | X0120_LOCK_CONTROL, 0x02000000]
Purpose: Execute lock operation (lock/unlock)
```

### Read Lock Progress
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0021_LOCK_CONTROL_PROGRESS]
Purpose: Get the progress of current lock operation
```

### Read Lock Status
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0020_LOCK_CONTROL_STATUS]
Purpose: Get the current status of the lock
```

## User Management Commands

### Get User Count
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0030_USER_COUNT]
Purpose: Get the total number of users registered in the device
```

### Set Username
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x91080000 | X1801_USERNAME, username_bytes_1, username_bytes_2]
Purpose: Set the username for the current session
Note: Username is encoded as ASCII characters, 4 chars per 32-bit word
```

### Read Username
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x11200000 | X1801_USERNAME]
Purpose: Get the current username
```

### Set Short Username (8 characters)
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x91080000 | X1801_USERNAME, username_bytes_1, username_bytes_2]
Purpose: Set a short username (8 characters) for the current session
```

### Set Long Username
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x91080000 | X1801_USERNAME, username_bytes_1, username_bytes_2]
Purpose: Set a long username for the current session
Note: Supports longer usernames up to the maximum allowed length
```

### Select User Type
```
Command: [0x60000000, 0x000c0003, 0x00000000, 0x83010000 | X1900_USER_SELECT, user_type]
Purpose: Select user type (normal/supervisor)
Values:
- 0x01000000: Normal user
- 0xfe000000: Supervisor
```

## Key Management Commands

### Send Lock Key
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x90100000 | X1902_LOCK_KEY, key_bytes_1, key_bytes_2, key_bytes_3, key_bytes_4]
Purpose: Send encryption key to the device
```

### Check Lock Key
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x10000000 | X1903_LOCK_KEY_CHECK]
Purpose: Verify if the sent key is correct
```

### Store Lock Key
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x87080000 | X1901_LOCK_KEY_STORE, 0x00000000, LOCK_KEY_WRITE_COMMAND]
Purpose: Store the current key in device memory
```

## Log Management Commands

### Select Log Entry
```
Command: [0x60000000, 0x000a0003, 0x00000000, 0x85020000 | X1811_LOG_SELECT, entry_number]
Purpose: Select a specific log entry for reading
```

### Read Log Status
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x07000000 | X1812_LOG_STATUS]
Purpose: Get the status of the selected log entry
```

### Read Log Date
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x08000000 | X1813_LOG_DATE]
Purpose: Get the date of the selected log entry
```

### Read Log Username
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x11000000 | X1814_LOG_USER]
Purpose: Get the username associated with the selected log entry
```

## Date/Time Management Commands

### Set Date/Time
```
Command: [0x60000000, 0x00100003, 0x00000000, 0x88080000 | X1800_DATE, 0x00000000, timestamp]
Purpose: Set the device's current date and time
```

### Read Date/Time
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x08000000 | X1800_DATE]
Purpose: Get the device's current date and time
```

## System Maintenance Commands

### Reset Lock Key
```
Command: [0x60000000, 0x000c0003, 0x00000000, 0x83010000 | XF002_SCRATCH8, 0x01000000]
Purpose: Reset the lock key settings
```

### Reset Log
```
Command: [0x60000000, 0x000c0003, 0x00000000, 0x83010000 | XF002_SCRATCH8, 0x02000000]
Purpose: Reset the device logs
```

## Sensor Reading Commands

### Read Temperature
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x04000000 | X0080_TEMPERATURE]
Purpose: Get the current temperature reading from the device
```

## Device Configuration Commands

### Set Configuration Method
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1000_CONFIG_METHOD]
Purpose: Set the device configuration method
```

### Set Voltage Clamp
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1001_CONFIG_VCLAMP]
Purpose: Configure voltage clamp settings
```

### Set Start Voltage
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1002_CONFIG_VOLT_VSTART]
Purpose: Set the starting voltage level
```

### Set Stop Voltage
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1003_CONFIG_VOLT_VSTOP]
Purpose: Set the stopping voltage level
```

### Set ON Time
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1004_CONFIG_TIME_TON]
Purpose: Configure the ON time duration
```

### Set OFF Time
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1005_CONFIG_TIME_TOFF]
Purpose: Configure the OFF time duration
```

### Set Single Start Voltage
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1006_CONFIG_SINGLE_VSTART]
Purpose: Set single operation start voltage
```

### Set Total Time
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x83010000 | X1007_CONFIG_TIME_TOTAL]
Purpose: Set the total operation time
```

## Charging Status Commands

### Read Raw Charge Value
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0010_CHARGE_RAW]
Purpose: Get the raw charging value from the device
Note: Returns the current raw charge reading
```

### Read Charge Threshold
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0011_CHARGE_RAW_THRESHOLD]
Purpose: Get the charging threshold value
Note: Returns the raw value that represents a full charge
```

### Read Charge Percentage
```
Command: [0x60000000, 0x00080001, 0x00000000, 0x03010000 | X0012_CHARGE_PERCENT]
Purpose: Get the current charge level as a percentage
Response: 0-100 representing charge percentage
Note: Calculated as (raw_charge * 100) / threshold, capped at 100%
```

## Command Types Reference

Common command types used in the Command ID field:

```
0x07000000 - Read command
0x08000000 - Read date/time
0x09000000 - Read system info
0x83010000 - Write/Control command
0x85020000 - Log control
0x87080000 - Key store
0x88080000 - Date/time control
0x90100000 - Key operation
0x91080000 - User control
```

## Notes on Command Usage

1. All multi-byte values are in big-endian format
2. Command length must be 4-byte aligned
3. Command Encryption:
   - Commands can be sent in encrypted or unencrypted format
   - Encrypted commands use flag 0x8001 instead of 0x0001
   - When encrypted, all data after the first 3 words is encrypted
   - Encryption uses the currently set device key
4. Always check command response for success/failure status
5. Some commands require prior authentication or specific device state
6. Username commands support different length formats:
   - Short format: 8 characters
   - Long format: Up to maximum allowed length (40 characters)
7. User type selection affects available commands and permissions





