###################################################################################################
# Variables
###################################################################################################
REPO_ROOT_DIR := $(abspath ../..)
PROJECT_ROOT_DIR := $(abspath .)
BUILD_DIR := $(abspath .)/build

# The 'working' directory of the unit tests is the 'test' folder (e.g. dp3_lib/test).
# That means, the unit tests are built and run from the 'test' folder, not from some other place.
# Consequently, in 'test', there is a makefile which includes this one.
# So, TEST_SOURCE_DIR is set to . because all test_*.c are found here.
TEST_SOURCE_DIR          := .
TEST_SUPPORT_DIR         := $(abspath .)/support

# removed     $(filter %/, $(wildcard $(PROJECT_ROOT_DIR)/libs/dp3_lib/inc/*/))
# seems to be a copy&paste error
PROJECT_SOURCE_DIRS := \
    $(PROJECT_ROOT_DIR)/../../smack_rom/libs/CMSIS/ifx/smack_series/inc \
    $(PROJECT_ROOT_DIR)/../../smack_rom/libs/smack_lib/inc \
    $(PROJECT_ROOT_DIR)/../../smack_rom/libs/smack_lib/inc/gen \
    $(PROJECT_ROOT_DIR)/../src \
    $(PROJECT_ROOT_DIR)/../../smack_rom/libs/smack_lib/test/support \
    $(filter %/, $(wildcard $(PROJECT_ROOT_DIR)/../../libs/smack_lib/inc/*/))

###################################################################################################
# Unit Test Framework Includes
###################################################################################################
include $(REPO_ROOT_DIR)/tools/intern/test/support/MakefileTest.mk

###################################################################################################
# Test Targets
###################################################################################################

# mandatory test doubles: Any test will not link w/o them.
DEFAULT_OBJS := \
    $(call obj, dbg_print) \
    $(call obj, fake_environment)

# Any unit test depends on
# a) the source file under test (SUT or DUT)
# b) any mocks it might need 
# c) any fakes it might need. Fakes are implemented in source files named as 'fake_xx.y'
# 
# So, in order to build a unit test executable, we need a target like this:
# path_to_exe/test_otp_records.exe:
#   path_to_fake_objects/fake_memory.o
#   path_to_source_under_test_objects/otp_records.o

$(call test, test_external_eeprom): \
    $(DEFAULT_OBJS) \
    $(call obj, external_eeprom) \
    $(call obj, patch_crc16_ccitt) \
    $(call obj, fake_spi_for_external_eeprom) \
    $(call obj, fake_ram)

$(call test, test_pattch_select_boot_memory): \
    $(DEFAULT_OBJS) \
    $(call obj, fake_hal_api_mem_mapper) \
    $(call obj, fake_ram) \
    $(call obj, fake_external_eeprom) \
    $(call obj, patch_select_boot_memory)

$(call test, test_spi): \
    $(DEFAULT_OBJS) \
    $(call obj, fake_hal_api_mem_mapper) \
    $(call obj, spi)

$(call test, test_cmd_eeprom): \
    $(DEFAULT_OBJS) \
    $(call obj, fake_hal_api_mem_mapper) \
    $(call obj, fake_ram) \
    $(call obj, cmd_eeprom)
    
$(call test, test_compatibility): \
    $(DEFAULT_OBJS)
