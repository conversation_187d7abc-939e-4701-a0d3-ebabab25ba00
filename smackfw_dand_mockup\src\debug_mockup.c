/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     debug_mockup.c
 *  @brief    Smack demo firmware for smartphone app development
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include <stdint.h>
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "inet_lib.h"
#include "aes_lib.h"
#include "smack_exchange.h"

// Smack firmware project
#include "smack_dand_data.h"
#include "smack_dand_mockup.h"

#if defined DEBUG && DEBUG

#include "version.h"
#include "uart_drv.h"
#include "uart_lib.h"
#include "printf.h"

// set UART baud rate for test reports
#define UART_BAUDRATE   115200


static void debug_mbox_inject(uint8_t cmd);


//---------------------------------------------------------

#define DBG_LOG_COUNT   50

char debug_cmd = 0;
static volatile char debug_cmd2 __attribute__((used));

static const aes_block_t debug_default_key = {{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}};
static const aes_block_t debug_user_key = {{0x00, 0x02, 0x04, 0x06, 0x08, 0x0a, 0x0c, 0x0e, 0x0f, 0x0d, 0x0b, 0x09, 0x07, 0x05, 0x03, 0x01}};
static const aes_block_t debug_su_key = {{0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff}};
static const aes_block_t debug_su_key2 = {{0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66}};
static const aes_block_t* debug_key = &debug_default_key;
static bool debug_encrypt = false;

static uint32_t dbg_tx_buf[64], dbg_rx_buf[64];


static struct
{
    uint16_t datapoint;
    uint16_t index;
    uint32_t value;
    uint8_t type;
    bool tx;
} logs[DBG_LOG_COUNT];
static uint16_t logs_read, logs_write;

void debug_init(void)
{
    uart_enable();
    uart_set_baudrate(UART_BAUDRATE);
    logs_read = logs_write = 0;

    printf("\n\nHello Smack!\n");
    printf("V%u.%u.%u.%u\n", (fw_version >> 24) & 0xff, (fw_version >> 16) & 0xff, (fw_version >> 8) & 0xff, fw_version & 0xff);
}

void debug_log_de(bool tx, uint16_t datapoint, uint16_t index)
{
    uint16_t idx;

    idx = logs_write + 1;

    if (idx >= DBG_LOG_COUNT)
    {
        idx = 0;
    }

    logs[idx].tx = tx;
    logs[idx].datapoint = datapoint;
    logs[idx].index = index;

    if (index >= 0 && index < data_point_count)
    {
        logs[idx].type = data_point_list[index].data_type;

        switch (data_point_list[index].data_type & DATA_POINT_TYPE_MASK)
        {
            case data_point_bool:
                logs[idx].value = *(bool*)data_point_list[index].value;
                break;

            case data_point_int8:
            case data_point_uint8:
                logs[idx].value = *(uint8_t*)data_point_list[index].value;
                break;

            case data_point_int16:
            case data_point_uint16:
                logs[idx].value = *(uint16_t*)data_point_list[index].value;
                break;

            case data_point_int64:
            case data_point_uint64:
                logs[idx].value = *(uint64_t*)data_point_list[index].value;
                break;

            case data_point_array:
            case data_point_string:
                {
                    uint8_t* p = (uint8_t*)data_point_list[index].value;
                    logs[idx].value = ((uint32_t)p[3] << 24) | ((uint32_t)p[2] << 16) | ((uint32_t)p[1] << 8) | (uint32_t)p[0];
                }
                break;

//    case data_point_int32:
//    case data_point_uint32:
            default:
                logs[idx].value = *(uint32_t*)data_point_list[index].value;
        }
    }

    __DMB();
    logs_write = idx;
}

void debug_task(void)
{
    static uint8_t percent;

    while (logs_read != logs_write)
    {
        logs_read++;

        if (logs_read >= DBG_LOG_COUNT)
        {
            logs_read = 0;
        }

        printf("de(%s):x%x idx:%u x%x\n", logs[logs_read].tx ? "tx" : "rx", logs[logs_read].datapoint, logs[logs_read].index, logs[logs_read].value);
    }

    if (debug_cmd)
    {
        debug_mbox_inject(debug_cmd);
        debug_cmd = 0;
    }

    if (percent != lock.control_progress)
    {
        percent = lock.control_progress;
        dbgp("progress:%3u\n", percent);
    }
}

static const uint32_t dbg_mbox_read_platform[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x07000000 | X0002_FW_PLATFORM,
};

static const uint32_t dbg_mbox_read_version[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x07000000 | X0001_FW_VERSION,
};

static const uint32_t dbg_mbox_read_uid[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x09000000 | X0004_UID,
};

static const uint32_t dbg_mbox_read_lockid[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x09000000 | X0005_LOCK_ID,
};

static const uint32_t dbg_mbox_lock_arm[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | X0121_LOCK_ARM,
    0x02000000,
};

static const uint32_t dbg_mbox_lock_exec[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | X0120_LOCK_CONTROL,
    0x02000000,
};

static const uint32_t dbg_mbox_motor_progress[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x03010000 | X0021_LOCK_CONTROL_PROGRESS,
    0x02000000,
};

static const uint32_t dbg_mbox_motor_status[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x03010000 | X0020_LOCK_CONTROL_STATUS,
};

static const uint32_t dbg_mbox_log_select0[] =
{
    0x60000000,
    0x000a0003,
    0x00000000,
    0x85020000 | X1811_LOG_SELECT,
    0x00000000,
};

static const uint32_t dbg_mbox_log_select1[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x85020000 | X1811_LOG_SELECT,
    0x00010000,
};

static const uint32_t dbg_mbox_log_read_status[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x07000000 | X1812_LOG_STATUS,
};

static const uint32_t dbg_mbox_log_read_date[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x08000000 | X1813_LOG_DATE,
};

static const uint32_t dbg_mbox_log_read_username[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x11000000 | X1814_LOG_USER,
};

static const uint32_t dbg_mbox_set_date[] =
{
    0x60000000,
    0x00100003,
    0x00000000,
    0x88080000 | X1800_DATE,
    0x00000000,
    0x6191a300,
};

static const uint32_t dbg_mbox_read_date[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x08000000 | X1800_DATE,
};

static const uint32_t dbg_mbox_set_username[] =
{
    0x60000000,
    0x00100003,
    0x00000000,
    0x91080000 | X1801_USERNAME,
    (('n' << 24) | ('e' << 16) | ('w' << 8) | ' '),
    (('U' << 24) | ('s' << 16) | ('e' << 8) | 'r'),
};

static const uint32_t dbg_mbox_read_username[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x11200000 | X1801_USERNAME,
};

static const uint32_t dbg_mbox_set_username_short8[] =
{
    0x60000000,
    0x00100003,
    0x00000000,
    0x91080000 | X1801_USERNAME,
    (('s' << 24) | ('h' << 16) | ('o' << 8) | 'r'),
    (('t' << 24) | ('8' << 16) | (0   << 8) | 0),
};

static const uint32_t dbg_mbox_set_username_long[] =
{
    0x60000000,
    0x00300003,
    0x00000000,
    0x91280000 | X1801_USERNAME,
    (('l' << 24) | ('o' << 16) | ('n' << 8) | 'g'),
    ((' ' << 24) | ('u' << 16) | ('s' << 8) | 'e'),
    (('r' << 24) | ('n' << 16) | ('a' << 8) | 'm'),
    (('e' << 24) | (' ' << 16) | ('(' << 8) | '4'),
    (('0' << 24) | (' ' << 16) | ('c' << 8) | 'h'),
    (('a' << 24) | ('r' << 16) | ('a' << 8) | 'c'),
    (('t' << 24) | ('e' << 16) | ('r' << 8) | 's'),
    ((')' << 24) | (' ' << 16) | ('w' << 8) | 'a'),
    (('i' << 24) | ('t' << 16) | ('.' << 8) | '.'),
    (('s' << 24) | ('t' << 16) | ('o' << 8) | 'p'),
};

static const uint32_t dbg_lock_key_send[] =
{
    0x60000000,
    0x00100003,
    0x00000000,
    0x90100000 | X1902_LOCK_KEY,
    0x00020406,
    0x080a0c0e,
    0x0f0d0b09,
    0x07050301,
};

static const uint32_t dbg_lock_key_check[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x10000000 | X1903_LOCK_KEY_CHECK,
};

static const uint32_t dbg_lock_key_store[] =
{
    0x60000000,
    0x00100003,
    0x00000000,
    0x87080000 | X1901_LOCK_KEY_STORE,
    0x00000000,
    LOCK_KEY_WRITE_COMMAND,
};

static const uint32_t dbg_mbox_reset_lockkey[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | XF002_SCRATCH8,
    0x01000000,
};

static const uint32_t dbg_mbox_reset_log[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | XF002_SCRATCH8,
    0x02000000,
};

static const uint32_t dbg_mbox_user_normal[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | X1900_USER_SELECT,
    0x01000000,
};

static const uint32_t dbg_mbox_user_supervisor[] =
{
    0x60000000,
    0x000c0003,
    0x00000000,
    0x83010000 | X1900_USER_SELECT,
    0xfe000000,
};

static const uint32_t dbg_mbox_read_mval_temp[] =
{
    0x60000000,
    0x00080001,
    0x00000000,
    0x04000000 | X0080_TEMPERATURE,
};

#define debug_call_app(a) debug_call_app_func_crypt(a, sizeof(a))

static void debug_call_app_func(const uint32_t* buf, uint32_t size)
{
    Mailbox_t* mbox;

    mbox = get_mailbox_address();
    memset(mbox, 0xee, get_mailbox_size() * sizeof(uint32_t));
    memcpy(mbox, buf, size);
    mailbox_message_valid(mbox);
}

static void dbg_smack_exchange_crypt(void* data, const uint16_t length, const bool crypt)
{
    aes_block_t* blocks;
    uint16_t block_cnt, i, j;

    if (length == 0)
    {
        return;
    }

    blocks = data;
    block_cnt = (length + 15) / 16;

    if (block_cnt > (sizeof(Mailbox_t) - 8) / sizeof(aes_block_t))
    {
        block_cnt = (sizeof(Mailbox_t) - 8) / sizeof(aes_block_t);
    }

    if (crypt)
    {
        // encrypt: XOR plaintext with cyphertext of previous block, then encrypt
        // salt must be added by the caller because we do not know about the struct of the data record here
        calc_aes_ba(&blocks[0], &blocks[0], encrypt);

        for (i = 1; i < block_cnt; i++)
        {
            for (j = 0; j < sizeof(blocks[0].w) / sizeof(blocks[0].w[0]); j++)
            {
                blocks[i].w[j] ^= blocks[i - 1].w[j];
            }

            calc_aes_ba(&blocks[i], &blocks[i], encrypt);
        }
    }
    else
    {
        // decrypt: decrypt block, then de-XOR plaintext with ciphertext of previous block
        if (block_cnt > 1) for (i = block_cnt - 1; i > 0; i--)
            {
                calc_aes_ba(&blocks[i], &blocks[i], decrypt);

                for (j = 0; j < sizeof(blocks[0].w) / sizeof(blocks[0].w[0]); j++)
                {
                    blocks[i].w[j] ^= blocks[i - 1].w[j];
                }
            }

        calc_aes_ba(&blocks[0], &blocks[0], decrypt);
    }
}

static void debug_call_app_func_crypt(const uint32_t* buf, uint32_t size)
{
    Mailbox_t* mbox;
    bool crypt = false;
    uint32_t payload;
    uint16_t i;

    if ((buf[1] & (1U << 15)))
    {
        crypt = true;
    }

    payload = 0;

    if (size > 8)
    {
        payload = size - 8;
    }

    dbg_rx_buf[0] = htonl(buf[0]);
    dbg_rx_buf[1] = htonl(buf[1]);

    if (debug_encrypt)
    {
        dbg_rx_buf[1] |= htonl(1U << 15);
        crypt = true;
    }

    generate_random_number_fast((void*)&dbg_rx_buf[2]);
    generate_random_number_fast((void*)&dbg_rx_buf[6]);
    generate_random_number_fast((void*)&dbg_rx_buf[10]);

    for (i = 3; i < (payload + 3) / 4 + 2; i++)
    {
        dbg_rx_buf[i] = htonl(buf[i]);
    }

    if (crypt)
    {
        payload = (payload + 15) & 0xfffffff0;
        *((uint16_t*)&dbg_rx_buf[1]) = htons(payload);
        aes_load_key_ba(debug_key);

//        for (i = 2; i < payload / 4 + 2; i += 4)
//        {
//            calc_aes_ba((void*)&dbg_rx_buf[i], (void*)&dbg_rx_buf[i], encrypt);
//        }
        dbg_smack_exchange_crypt(&dbg_rx_buf[2], payload, true);

        smack_exchange_key_restore();
    }

    mbox = get_mailbox_address();
    memset(mbox, 0xee, get_mailbox_size() * sizeof(uint32_t));

    for (i = 0; i < (payload + 3) / 4 + 2; i++)
    {
        mbox->content[i] = ntohl(dbg_rx_buf[i]);
    }

    mailbox_message_valid(mbox);

    payload = mbox->content[1] >> 16;
    crypt = ((mbox->content[1] & (1U << 15)) != 0);
    memset(dbg_tx_buf, 0xdd, sizeof(dbg_tx_buf));

    for (i = 0; i < (payload + 3) / 4 + 2; i++)
    {
        dbg_tx_buf[i] = htonl(mbox->content[i]);
    }

    if (crypt)
    {
        aes_load_key_ba(debug_key);

//        for (i = 2; i < payload / 4 + 2; i += 4)
//        {
//            calc_aes_ba((void*)&dbg_tx_buf[i], (void*)&dbg_tx_buf[i], decrypt);
//        }
        dbg_smack_exchange_crypt(&dbg_tx_buf[2], payload, false);

        smack_exchange_key_restore();
    }
}

static void debug_mbox_inject(uint8_t cmd)
{
    switch (cmd)
    {
        case '0':
            debug_call_app(dbg_mbox_read_platform);
            debug_call_app(dbg_mbox_read_version);
            debug_call_app(dbg_mbox_read_uid);
            debug_call_app(dbg_mbox_read_lockid);
            break;

        case '1':
            debug_call_app(dbg_mbox_set_date);
            debug_call_app(dbg_mbox_set_username);
            break;

        case '2':
            debug_call_app(dbg_mbox_lock_arm);
            debug_call_app(dbg_mbox_lock_exec);
            break;

        case 'A': // x41/65
            debug_call_app(dbg_mbox_log_select0);
            break;

        case 'B': // x42/66
            debug_call_app(dbg_mbox_log_select1);
            break;

        case 'C': // x43/67
            debug_call_app(dbg_mbox_log_read_date);
            debug_call_app(dbg_mbox_log_read_username);
            debug_call_app(dbg_mbox_log_read_status);
            break;

        case 'c': // x43/67
            debug_call_app(dbg_mbox_log_read_date);
            break;

        case 'D': // x44/68
            debug_call_app(dbg_lock_key_send);
            debug_call_app(dbg_lock_key_check);
            break;

        case 'd': // x64/100
            debug_call_app(dbg_mbox_read_date);
            break;

        case 'E': // x45/69
            debug_call_app(dbg_lock_key_store);
            break;

        case 'F': // x46/70
            debug_call_app(dbg_mbox_reset_lockkey);
            break;

        case 'G': // x47/71
            debug_call_app(dbg_mbox_reset_log);
            break;

        case 'P':
            debug_call_app(dbg_mbox_motor_progress);
            break;

        case 'p':
            debug_call_app(dbg_mbox_motor_status);
            break;

        case 'U': // x55/65
            debug_key = &debug_default_key;
            break;

        case 'u':
            debug_key = &debug_su_key2;
            break;

        case 'V': // x56/66
            debug_key = &debug_user_key;
            break;

        case 'v':
            debug_call_app(dbg_mbox_user_normal);
            break;

        case 'W': // x57/67
            debug_key = &debug_su_key;
            break;

        case 'w':
            debug_call_app(dbg_mbox_user_supervisor);
            break;

        case 'Y': // x59/89
            debug_encrypt = true;
            break;

        case 'y': // x79/121
            debug_encrypt = false;
            break;

        case 'x': // x78/120
            debug_call_app(dbg_mbox_set_username_short8);
            break;

        case 'X': // x58/88
            debug_call_app(dbg_mbox_set_username_long);
            break;

        case 'Z': // x5a/90
            debug_call_app(dbg_mbox_read_username);
            break;

        case 'T': // x5a/90
            debug_call_app(dbg_mbox_read_mval_temp);
            break;
    }

    debug_cmd2 = (char)debug_encrypt;
}

#endif
