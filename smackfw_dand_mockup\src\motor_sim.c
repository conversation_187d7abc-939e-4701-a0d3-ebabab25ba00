/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     motor_sim.c
 *  @brief    Smack motor simulation
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include "core_cm0.h"
#include <stdbool.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib

// Smack stepwise project
#include "motor_common.h"
#include "motor_sim.h"


// prototypes
static motor_rc_t motor_init(void);
static motor_rc_t motor_exit(void);
static motor_rc_t motor_task(void);
static motor_rc_t motor_control(smack_motor_command_t command);
static motor_rc_t motor_config(void);


// globals
smack_motor_control_status_t motor_sim_status;

const smack_motor_control_t motor_sim_control =
{
    .init = motor_init,
    .exit = motor_exit,
    .task = motor_task,
    .control = motor_control,
    .config = motor_config,
};


typedef enum motor_state_e
{
    s_lock_init = 0,
    //s_lock_idle,
    s_lock_charge,
    s_lock_open,
    s_lock_open_recharge,
    s_lock_close,
    s_lock_close_recharge,
} motor_state_t;

// local
static struct
{
    uint32_t lastTick;
    uint32_t t_total;   // total motor runtime
    uint32_t runtime_acc;    // accumulated runtime
    motor_state_t state;
    smack_motor_command_t command;
} motor_state;


#define CHARGE(a)       (motor_sim_status.charge += (a), (motor_sim_status.charge > (motor_sim_status.charge_threshold - 111)) ? (motor_sim_status.charge = motor_sim_status.charge_threshold + 120) : motor_sim_status.charge)
#define DISCHARGE(a)    ((motor_sim_status.charge > (a)) ? (motor_sim_status.charge -= (a)) : (motor_sim_status.charge = 0))


static void hb_on(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(true, false, false, true);
}

static void hb_forward_off(void)
{
    set_hb_switch(true, false, false, false);
}

static void hb_reverse(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(false, true, true, false);
}

static void hb_reverse_off(void)
{
    set_hb_switch(false, true, false, false);
    set_hb_switch(false, false, false, false);
    set_hb_switch(true, false, false, false);
}

static void hb_off(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(true, false, false, false);
}

static motor_rc_t motor_init(void)
{
    motor_config();
    set_hb_eventctrl(false);
    hb_off();
    motor_state.state = s_lock_init;
    return 0;
}

static motor_rc_t motor_exit(void)
{
    set_hb_switch(false, false, false, false);
    motor_state.state = s_lock_init;
    return 0;
}

static motor_rc_t motor_config(void)
{
    motor_state.t_total = ms2ticks(motor_sim_status.t_total);
    motor_sim_status.progress_threshold = motor_state.t_total;
    motor_sim_status.charge_threshold = motor_sim_status.v_clamp;
    return 0;
}

static motor_rc_t motor_control(smack_motor_command_t command)
{
    motor_state.command = command;
    return 0;
}

static motor_rc_t motor_task(void)
{
//    static uint32_t count;
    uint32_t now;
    bool close, open, stop;
    motor_rc_t rc = 0;

    open = close = stop = false;
    __DMB();

    // should be enclosed in critical section
//    if (lock_control_stop /*|| (lock_control & ??)*/)
//    {
//        stop = true;
//    }
//    else
    if (motor_state.command == motor_ctrl_on)
    {
        close = true;
    }
    else if (motor_state.command == motor_ctrl_reverse)
    {
        open = true;
    }

    motor_state.command = 0;

// TODOs:
// - start lock operation only when cap is fully charged
// - add overall timeout
//    lock_control_lock = lock_control_unlock = lock_control_stop = false;
    //lock.control &= ~(lock_control_locked | lock_control_unlocked);
    __DMB();

    switch (motor_state.state)
    {
        case s_lock_charge:
            CHARGE(50);

            if (open)
            {
//                count = 0;
                motor_sim_status.status = motor_status_moving;
                motor_sim_status.progress = 0;
                motor_state.runtime_acc = 0;
//                motor_status_set(lock_control_operating);
                hb_on();
                motor_state.state = s_lock_open;
                //log_write(lock_control_unlocked);
                motor_state.lastTick = smack_motor_services.timer_get();
            }
            else if (close)
            {
//                count = 0;
                motor_sim_status.status = motor_status_moving;
                motor_sim_status.progress = 0;
                motor_state.runtime_acc = 0;
//                motor_status_set(lock_control_operating);
                hb_reverse();
                motor_state.state = s_lock_close;
                //log_write(lock_control_locked);
                motor_state.lastTick = smack_motor_services.timer_get();
            }

            break;

        case s_lock_open:
            DISCHARGE(20);
            now = smack_motor_services.timer_get();
            motor_sim_status.progress = now - motor_state.lastTick + motor_state.runtime_acc;

            if (motor_sim_status.progress >= motor_sim_status.progress_threshold)
            {
                hb_forward_off();
                motor_sim_status.status = motor_status_pos1;
//                motor_status_set(lock_control_unlocked);
                motor_state.state = s_lock_charge;
            }
            else
            {
                if (motor_sim_status.charge <= motor_sim_status.param2)
                {
                    hb_forward_off();
                    motor_state.runtime_acc += now - motor_state.lastTick;
                    motor_state.state = s_lock_open_recharge;
                }
            }

            break;

        case s_lock_open_recharge:
            CHARGE(40);

            if (motor_sim_status.charge >= motor_sim_status.param1)
            {
                hb_on();
                motor_state.lastTick = smack_motor_services.timer_get();
                motor_state.state = s_lock_open;
            }

            break;

        case s_lock_close:
            DISCHARGE(20);
            now = smack_motor_services.timer_get();
            motor_sim_status.progress = now - motor_state.lastTick + motor_state.runtime_acc;

            if (motor_sim_status.progress >= motor_sim_status.progress_threshold)
            {
                hb_reverse_off();
                motor_sim_status.status = motor_status_pos2;
//                motor_status_set(lock_control_unlocked);
                motor_state.state = s_lock_charge;
            }
            else
            {
                if (motor_sim_status.charge <= motor_sim_status.param2)
                {
                    hb_reverse_off();
                    motor_state.runtime_acc += now - motor_state.lastTick;
                    motor_state.state = s_lock_close_recharge;
                }
            }

            break;

        case s_lock_close_recharge:
            CHARGE(40);

            if (motor_sim_status.charge >= motor_sim_status.param1)
            {
                hb_reverse();
                motor_state.lastTick = smack_motor_services.timer_get();
                motor_state.state = s_lock_close;
            }

            break;

        case s_lock_init:
        default:
            motor_state.state = s_lock_charge;
    }

    return rc;
}
