#include "util.h"

#define TIMER_CLOCK 5

// Delay function based on SysTick
// The systick_singleshot_lib() function is using the WFI function during the delay to save power
void delay_ticks(uint32_t ticks)
{
    // the SysTick timer has a width of 24 bits. For longer delays, loop over the maximum delay count
    while (ticks > 0x00ffffff)
    {
        systick_singleshot_lib(0x00ffffff);
        ticks -= 0x00ffffff;
    }

    // now wait for the remaining time
    if (ticks)
    {
        systick_singleshot_lib(ticks);
    }
}

// get random uint32_t number
uint32_t get_random_u32(uint32_t offset) {
  uint32_t tick = sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);
  uint32_t rssi_raw = 0;
  uint32_t vccca_raw = 0;
  Mailbox_t *p;
  p = get_mailbox_address();
  bool field_present = check_rf_field();
  if (field_present) {
    rssi_raw = message_get_rssi(p);
    vccca_raw = message_get_vccca(p);
  }
  srand(tick * rssi_raw * vccca_raw + offset);
  uint32_t r1 = (uint32_t)(rand() & 0x7FFF);
  uint32_t r2 = (uint32_t)(rand() & 0x7FFF);
  uint32_t r3 = (uint32_t)(rand() & 0x3);
  return (r3 << 30) | (r2 << 15) | r1;
}
