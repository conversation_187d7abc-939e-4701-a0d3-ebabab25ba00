#ifndef _BUCK_H_
#define _BUCK_H_

#include "util.h"

#define GPIO_PWM 1
#define GPIO_CHARGE_DETECT 3
#define GPIO_BUCK_EN 4

// Configure as On and Off times in unit microseconds
#define PWM_TON_US              7
#define PWM_TOFF_US             50

// range of PWM period: 1...65536 (16 bits, register is loaded with value - 1)
#define PWM_PERIOD_TICKS        ((PWM_TON_US + PWM_TOFF_US) * 28)

// # of ticks for HIGH phase, signal is low for remainder of period
// range: 1...65535; must be less then period
#define PWM_DUTY_TICKS          (PWM_TON_US * 28)

// The following definitions of PWM_PERIOD_HZ and PWM_DUTY_PERCENT are only used if the definitions of PWM_PERIOD_TICKS and PWM_DUTY_TICKS are disabled (commented)

// period defined as frequency, range of calculated tick count must fit into 16 bit register (e.g. frequency shall be higher than 500Hz)
#define PWM_PERIOD_HZ           100000

// duty cycle in percent of period
#define PWM_DUTY_PERCENT        20

#ifndef PWM_PERIOD_TICKS
#define PWM_PERIOD_TICKS        ((XTAL + PWM_PERIOD_HZ / 2) / PWM_PERIOD_HZ)
#endif
#ifndef PWM_DUTY_TICKS
#define PWM_DUTY_TICKS          ((PWM_PERIOD_TICKS * PWM_DUTY_PERCENT + 50) / 100)
#endif

void buck_init();

void buck_process();

void buck_complete();

#endif