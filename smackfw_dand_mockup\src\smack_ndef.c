/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     smack_ndef.c
 *  @brief    Smack demo firmware for smartphone app development
 *
 *  This example shall provide the customized NDEF record.
 */

// standard libs
#include <stdint.h>

// Smack NVM project
#include "settings.h"
#include "smack_ndef.h"


/* NDEF tag defined by user.
 * To activate this tag, set the field "tag_type_2_ptr" in aparams.
 */


/* When selecting entries from the templates below, please make sure to configure the proper payload length,
 * and to set MB in the first NDEF record, and ME in the last.
 */

#define NDEF_TAG_PAYLOAD_SIZE   (0x14)

/* TNF byte:
 * bit 7:   MB (message begin)
 * bit 6:   ME (message end)
 * bit 5:   CF (chunk flag)
 * bit 4:   SR (short record)
 * bit 3:   IL (ID length present)
 * bit 0-2: TNF
 */

__attribute__((used, section(".nvm_ndef")))
const uint8_t smack_sl_tag[] =                    /**< [0x3a0:0x3ff] 96 Bytes Tag2 area          */
{
    0x05, 0xc0, 0xbe, 0xef,      /**< BLOCK0 UID0,1,2,3,4 */
    0xde, 0xad, 0x00, 0x00,      /**< BLOCK1 UID5,6,7,0x00 */
    0xff, 0xff, 0xff, 0xff,      /**< BLOCK2 Internal Lock  Byte0 and Byte1 are relevant 0xff means read only*/
    0xE1,                        /**< Capability Container CC_0 Magic Number fixed to 0xE1 for type 2 Tag */
    0x10,                        /**< Capability Container CC_1 Mapping version default 0x10 */
    0x0f,                        /**< Capability Container CC_2 size (Blocks of 8 bytes each) */
    0x0f,                        /**< Capability Container CC_3 Access Conditions , 0x0f  for read only tag w/o security*///

    // NDEF TLV
    0x03,                       // TLV: type NDEF
    NDEF_TAG_PAYLOAD_SIZE,      // TLV: length

#if 1
    0xd1,
    0x01,
    0x10,
    'T',
    0x02, 'e', 'n',
    '#', '*', 'v', 'n', 'd', 'l', 'c', 'l', 'o', 'c', 'k', '*', '#',
#endif
#if 0
    0xd4,                       // MB + SR + TNF=4
    0x0f,                       // record name length
    0x1a,                       // payload length
    // record name:
    'a', 'n', 'd', 'r',
    'o', 'i', 'd', '.',
    'c', 'o', 'm', ':',
    'p', 'k', 'g',
    // payload:
    'c', 'o', 'm', '.',
    'l', 'v', 'c', 'h',
    'e', 'n', 'g', '.',
    'n', 'f', 'c', 'l',
    'o', 'c', 'k', '.',
    's', 't', 'a', 'b',
    'l', 'e',
#endif
#if 0
    // length = 3 + 1 + 47 = 51
    // NDEF tag: text
    0xd1,                       // MB + SR + TNF=1
    0x01,                       // record name length
    47,                         // payload length
    'T' /*0x54*/,               // record name: "T" = text
    // payload:
    0x02,  'e', 'n',            // language ID length, language
    'I', 'n', 'f', 'i',
    'n', 'e', 'o', 'n',
    ' ', 'S', 'm', 'A',
    'c', 'K', ' ', 'S',
    'h', 'o', 'w', 'c',
    'a', 's', 'e', ' ',
    'v', '0' + (FIRMWARE_VERSION_MAJOR / 10) % 10, '0' + FIRMWARE_VERSION_MAJOR % 10, '.',
    '0' + (FIRMWARE_VERSION_MINOR / 10) % 10, '0' + FIRMWARE_VERSION_MINOR % 10, '.', '0' + (FIRMWARE_VERSION_STEP / 10) % 10,
    '0' + FIRMWARE_VERSION_STEP % 10, '.', '0' + (FIRMWARE_VERSION_BUILD / 10) % 10, '0' + FIRMWARE_VERSION_BUILD % 10,
#if SMACK_BOARD == BOARD_EVAL_V22
    ' ', '(', 'H', 'W', '2', '.', '2', ')',
#elif SMACK_BOARD == BOARD_BOOST_BUCK_V30
    ' ', '(', 'H', 'W', '3', '.', '0', ')',
#elif SMACK_BOARD == BOARD_EVAL_V50_MINI
    ' ', '(', 'H', 'W', '5', '.', '0', ')',
#else
    ' ', '(', 'H', 'W', '?', '.', '?', ')',
#endif
#endif
#if 0
    // length = 3 + 1 + 0x1a = 0x1e (30)
    // NDEF tag: text
    0xd1,                       // MB + SR + TNF=1
    0x01,                       // record name length
    0x1a,                       // payload length
    'T' /*0x54*/,               // record name: "T" = text
    // payload:
    0x02,  'e', 'n',            // language ID length, language
    'I', 'n', 'f', 'i',
    'n', 'e', 'o', 'n',
    ' ', 'S', 'm', 'A',
    'c', 'K', ' ', 'S',
    'h', 'o', 'w', 'c',
    'a', 's', 'e',
#endif
#if 0
    // NDEF tag: text
    0x51,                       // ME + SR + TNF=1
    0x01,                       // record name length
    0x1e,                       // payload length
    'T' /*0x54*/,               // record name: "T" = text
    // payload:
    0x02,  'e', 'n',            // language ID length, language
    'c',  'o',  'm',             /**< BLOCK6  lang1, payload .... */
    '.',   'i',  'n',  'f',      /**< BLOCK7  */
    'i',   'n',  'e',  'o',      /**< BLOCK8  */
    'n',   '.',  's',  'm',      /**< BLOCK9  */
    'a',   'c',  'k',  '.',      /**< BLOCK10 */
    's',   'h',  'o',  'w',      /**< BLOCK11 */
    'c',   'a',  's',  'e',      /**< BLOCK12 */ // 27 bytes
#endif
#if 0
    // NDEF tag: URI
    0x11,                       // SR + TNF=1
    0x01,                       // record name length
    0x0d,                       // payload length (abbriviation through URI)
    'U',                        // record name: "U" = URI
    // payload:
    0x02,                       // abbreviation: "https://www/"
    'i', 'n', 'f', 'i',         // URI
    'n', 'e', 'o', 'n',
    '.', 'c', 'o', 'm',
#endif
#if 0
    // length: 3 + 0x0f + 0x1b = 0x2d
    // NDEF tag: android.com:pkg
    0x54,                       // ME + SR + TNF=4
    0x0f,                       // record name length
    0x1b,                       // payload length
    // record name:
    'a', 'n', 'd', 'r',
    'o', 'i', 'd', '.',
    'c', 'o', 'm', ':',
    'p', 'k', 'g',
    // payload:
    'c', 'o', 'm', '.',
    'i', 'n', 'f', 'i',
    'n', 'e', 'o', 'n',
    '.', 's', 'm', 'a',
    'c', 'k', '.', 's',
    'h', 'o', 'w', 'c',
    'a', 's', 'e',
#endif

    0xfe,                       // TLV: Terminator
};
