This directory contains the 3.81 release of GNU Make.

See the file NEWS for the user-visible changes from previous releases.
In addition, there have been bugs fixed.

Please check the system-specific notes below for any caveats related to
your operating system.

For general building and installation instructions, see the file INSTALL.

If you need to build GNU Make and have no other `make' program to use,
you can use the shell script `build.sh' instead.  To do this, first run
`configure' as described in INSTALL.  Then, instead of typing `make' to
build the program, type `sh build.sh'.  This should compile the program
in the current directory.  Then you will have a Make program that you can
use for `./make install', or whatever else.

Some systems' Make programs are broken and cannot process the Makefile for
GNU Make.  If you get errors from your system's Make when building GNU
Make, try using `build.sh' instead.


GNU Make is free software.  See the file COPYING for copying conditions.


Downloading
-----------

GNU Make can be obtained in many different ways.  See a description here:

  http://www.gnu.org/software/software.html


Documentation
-------------

GNU make is fully documented in the GNU Make manual, which is contained
in this distribution as the file make.texinfo.  You can also find
on-line and preformatted (PostScript and DVI) versions at the FSF's web
site.  There is information there about ordering hardcopy documentation.

  http://www.gnu.org/
  http://www.gnu.org/doc/doc.html
  http://www.gnu.org/manual/manual.html


Development
-----------

GNU Make development is hosted by Savannah, the FSF's online development
management tool.  Savannah is here:

  http://savannah.gnu.org

And the GNU Make development page is here:

  http://savannah.gnu.org/projects/make/

You can find most information concerning the development of GNU Make at
this site.


Bug Reporting
-------------

You can send GNU make bug reports to <<EMAIL>>.  Please see the
section of the GNU make manual entitled `Problems and Bugs' for
information on submitting useful and complete bug reports.

You can also use the online bug tracking system in the Savannah GNU Make
project to submit new problem reports or search for existing ones:

  http://savannah.gnu.org/bugs/?group=make

If you need help using GNU make, try these forums:

  <EMAIL>
  <EMAIL>
  news:gnu.utils.help
  news:gnu.utils.bug

  http://savannah.gnu.org/support/?group=make

You may also find interesting patches to GNU Make available here:

  http://savannah.gnu.org/patch/?group=make

Note these patches are provided by our users as a service and we make no
statements regarding their correctness.  Please contact the authors
directly if you have a problem or suggestion for a patch available on
this page.


CVS Access
----------

The GNU make source repository is available via anonymous CVS from the
GNU Subversions CVS server; look here for details:

  http://savannah.gnu.org/cvs/?group=make

Please note: you won't be able to build GNU make from CVS without
installing appropriate maintainer's tools, such as GNU m4, automake,
autoconf, Perl, GNU make, and GCC.  See the README.cvs file for hints on
how to build GNU make once these tools are available.  We make no
guarantees about the contents or quality of the latest code in the CVS
repository: it is not unheard of for code that is known to be broken to
be checked in.  Use at your own risk.


System-specific Notes
---------------------

It has been reported that the XLC 1.2 compiler on AIX 3.2 is buggy such
that if you compile make with `cc -O' on AIX 3.2, it will not work
correctly.  It is said that using `cc' without `-O' does work.

The standard /bin/sh on SunOS 4.1.3_U1 and 4.1.4 is broken and cannot be
used to configure GNU make.  Please install a different shell such as
bash or pdksh in order to run "configure".  See this message for more
information:
  http://mail.gnu.org/archive/html/bug-autoconf/2003-10/msg00190.html

One area that is often a problem in configuration and porting is the code
to check the system's current load average.  To make it easier to test and
debug this code, you can do `make check-loadavg' to see if it works
properly on your system.  (You must run `configure' beforehand, but you
need not build Make itself to run this test.)

Another potential source of porting problems is the support for large
files (LFS) in configure for those operating systems that provide it.
Please report any bugs that you find in this area.  If you run into
difficulties, then as a workaround you should be able to disable LFS by
adding the `--disable-largefile' option to the `configure' script.

On systems that support micro- and nano-second timestamp values and
where stat(2) provides this information, GNU make will use it when
comparing timestamps to get the most accurate possible result.  However,
note that many current implementations of tools that *set* timestamps do
not preserve micro- or nano-second granularity.  This means that "cp -p"
and other similar tools (tar, etc.) may not exactly duplicate timestamps
with micro- and nano-second granularity on some systems.  If your build
system contains rules that depend on proper behavior of tools like "cp
-p", you should consider using the .LOW_RESOLUTION_TIME pseudo-target to
force make to treat them properly.  See the manual for details.


Ports
-----

  - See README.customs for details on integrating GNU make with the
    Customs distributed build environment from the Pmake distribution.

  - See readme.vms for details about GNU Make on OpenVMS.

  - See README.Amiga for details about GNU Make on AmigaDOS.

  - See README.W32 for details about GNU Make on Windows NT, 95, or 98.

  - See README.DOS for compilation instructions on MS-DOS and MS-Windows
    using DJGPP tools.

    A precompiled binary of the MSDOS port of GNU Make is available as part
    of DJGPP; see the WWW page http://www.delorie.com/djgpp/ for more
    information.

Please note there are two _separate_ ports of GNU make for Microsoft
systems: a native Windows tool built with (for example) MSVC or Cygwin,
and a DOS-based tool built with DJGPP.  Please be sure you are looking
at the right README!


-------------------------------------------------------------------------------
Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997,
1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006 Free Software Foundation,
Inc.
This file is part of GNU Make.

GNU Make is free software; you can redistribute it and/or modify it under the
terms of the GNU General Public License as published by the Free Software
Foundation; either version 2, or (at your option) any later version.

GNU Make is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
A PARTICULAR PURPOSE.  See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with
GNU Make; see the file COPYING.  If not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
