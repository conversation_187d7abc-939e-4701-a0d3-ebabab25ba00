/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     smack_dand_nvm.c
 *  @brief    Smack demo firmware for smartphone app development
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include <stdint.h>
//#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
//#include "smack_exchange.h"

// Smack firmware project
#include "settings.h"
#include "smack_dand_mockup.h"  // get debug config
#include "smack_dand_data.h"
#include "smack_dand_nvm.h"
#include "smack_dand_nvm_data.h"


//---------------------------------------------------------
// persistent configuration


static uint8_t config_index = 0xff;
static uint8_t config_seq;

static uint8_t config_get_index(void)
{
    int16_t idx = -1;
    int16_t idx2;
    int i;

    // todo: check for wraparound break if count > 2
    for (i = 0; i < CONFIG_NVM_COUNT; i++)
    {
        if (config_nvm[i].magic == CONFIG_MAGIC)
        {
            idx2 = i - 1;

            if (idx2 < 0)
            {
                idx2 = CONFIG_NVM_COUNT - 1;
            }

            if (config_nvm[idx2].magic != CONFIG_MAGIC ||
                    (config_nvm[i].seq == (uint8_t)(config_nvm[idx2].seq + 1)))
            {
                idx = i;
            }
        }
    }

    if (idx >= 0)
    {
        config_index = idx;
        config_seq = config_nvm[idx].seq;
    }
    else
    {
        config_index = 0;
        config_seq = 0;
    }

    return config_index;
}

bool config_load(void)
{
    uint8_t index;
    bool rc;

    // TODO: get index
    index = config_get_index();

    if (config_nvm[index].magic == CONFIG_MAGIC)
    {
        config = config_nvm[index].config;
        rc = true;
    }
    else
    {
        config_set_default();
    }

    return rc;
}

void config_save(void)
{
    uint8_t index;
    config_nvm_t* p;
    static uint32_t rc __attribute__((unused));

    index = config_get_index();
    //dbgp("%s: idx=%u\n", __func__, index);

    if (memcmp(&config_nvm[index].config, &config, sizeof(config)))
    {
        index++;
        //dbgp("%s: writing idx=%u\n", __func__, index);

        if (index >= CONFIG_NVM_COUNT)
        {
            index = 0;
        }

        p = (config_nvm_t*)&config_nvm[index];
        rc = nvm_open_assembly_buffer((uint32_t)p);
        p->config = config;
        p->seq = config_seq + 1;
        p->magic = CONFIG_MAGIC;
        nvm_program_page();
        nvm_config();
        config_seq = p->seq;
        config_index = index;
    }
}

void config_set_default(void)
{
    // use temporary variable to prevent beautifier to scramble the layout... generates same code
    const smartlock_config_t cfg_default =
    {
        .volt.v_start = STEPWISE_VOLT_START,
        .volt.v_stop = STEPWISE_VOLT_STOP,
        .time.t_on = STEPWISE_TIME_ON,
        .time.t_off = STEPWISE_TIME_OFF,
        .single.v_start = ONESHOT_VOLT_START,
        .v_clamp = VCCHB_CLAMP_RAW,
        .t_total = TOTAL_MOTOR_RUNTIME,
        .method = MOTOR_CONTROL_METHOD,
    };
    config = cfg_default;
}


//---------------------------------------------------------
// user configuration (e.g. encryption keys)

// TODO: use more pages in NVM, currently only a single page is written

static uint8_t user_cfg_get_index(void)
{
    return 0;
}

void user_cfg_read(void)
{
    uint8_t index;
    uint8_t u;

    index = user_cfg_get_index();
    dbgp("%s: idx=%u\n", __func__, index);

    do
    {
        dbgp("p:%p/%p magic:%02x\n", &user_cfg_nvm[index], &user_cfg_nvm[index].magic, user_cfg_nvm[index].magic);

        if (user_cfg_nvm[index].magic != USER_CFG_MAGIC)
        {
            continue;
        }

        for (u = 0; u < sizeof(user_cfg_nvm[index].cfg.key2.w) / sizeof(user_cfg_nvm[index].cfg.key2.w[0]); u++)
        {
            dbgp("u:%u 1:%08x/2:%08x/~2:%08x\n", user_cfg_nvm[index].cfg.key.w[u], user_cfg_nvm[index].cfg.key2.w[u], ~user_cfg_nvm[index].cfg.key2.w[u]);

            if (user_cfg_nvm[index].cfg.key2.w[u] != ~user_cfg_nvm[index].cfg.key.w[u])
            {
                continue;
            }
        }

        smartlock_user = user_cfg_nvm[index].cfg; // TODO: decrypt}

        if (session.user_count == 0)
        {
            session.user_count = 1;
        }

        dbgp("%s: user cnt=%u\n", __func__, session.user_count);
    }
    while (0);
}

void user_cfg_write(void)
{
    user_cfg_nvm_t* p;
    aes_block_t key;
    uint8_t index, u;

    index = user_cfg_get_index();
    p = (user_cfg_nvm_t*)&user_cfg_nvm[index];
    dbgp("%s(1): p:%p magic:%02x data:%08x\n", __func__, p, ((volatile user_cfg_nvm_t)*p).magic, ((volatile user_cfg_nvm_t)*p).cfg.key.w[0]);

    for (u = 0; u < sizeof(smartlock_user.key2.w) / sizeof(smartlock_user.key2.w[0]); u++)
    {
        smartlock_user.key2.w[u] = ~smartlock_user.key.w[u];
    }

    nvm_config();       // just to be sure
    u = nvm_open_assembly_buffer((uint32_t)p);
    p->cfg = smartlock_user;    // TODO: encrypt
    p->seq = index;             // TODO: use sequence number
    p->magic = USER_CFG_MAGIC;
    nvm_program_page();
    nvm_config();
    dbgp("%s(2): p:%p magic:%02x data:%08x\n", __func__, p, ((volatile user_cfg_nvm_t)*p).magic, ((volatile user_cfg_nvm_t)*p).cfg.key.w[0]);
}

//---------------------------------------------------------
// log / history
// copy/paste of config functions (parts may be candidate for generic functions, will increase complexity)

// Two entries will fit into one NVM page. Nevertheless, we use on page per entry here as it allows simpler
// code and prevents aditional data loss if power disappears while trying to write the second entry within
// a page.


#warning TODO: update log_if count
static uint8_t log_index = 0xff;
static uint8_t log_count = 0;
static uint8_t log_seq;

__attribute__((noinline)) static uint8_t log_get_index(void)
{
    int16_t idx = -1;
    int16_t idx2;
    int i;

    dbgp("%s()\n", __func__);

    for (i = 0; i < LOG_NVM_COUNT; i++)
    {
        if (log_nvm[i].magic == LOG_MAGIC)
        {
            idx2 = i - 1;

            if (idx2 < 0)
            {
                idx2 = LOG_NVM_COUNT - 1;
            }

            if (log_nvm[idx2].magic != LOG_MAGIC ||
                    (log_nvm[i].seq == (uint8_t)(log_nvm[idx2].seq + 1)))
            {
                idx = i;
                idx2 = i + 1;           // check for end of linear sequence (e.g. wraparound range)

                if (idx2 >= LOG_NVM_COUNT || log_nvm[idx2].magic != LOG_MAGIC || log_nvm[idx2].seq != (uint8_t)(log_nvm[idx].seq + 1))
                {
                    break;
                }
            }
        }
    }

    if (idx >= 0)
    {
        log_index = idx;
        log_seq = log_nvm[idx].seq;
        log_count = 1;

        for (i = 0; i < LOG_NVM_COUNT - 1; i++)
        {
            idx2 = idx - 1;

            if (idx2 < 0)
            {
                idx2 = LOG_NVM_COUNT - 1;
            }

            if (log_nvm[idx2].magic != LOG_MAGIC ||
                    (log_nvm[idx].seq != (uint8_t)(log_nvm[idx2].seq + 1)))
            {
                break;
            }

            log_count++;
            idx = idx2;
        }
    }
    else
    {
        log_index = 0;
        log_count = 0;
        log_seq = 0;
    }

    log_if.count = log_count;
    dbgp("idx=%u cnt=%u\n", log_index, log_count);
    return log_index;
}

__attribute__((noinline)) static void log_clear(void)
{
    log_if.count = 0;
    log_if.select = 0;
    log_if.status = 0;
    log_if.date = 0;
    log_if.username[0] = '\0';
}

__attribute__((noinline)) bool log_load(void)
{
    uint8_t index;
    bool rc = false;

    index = log_get_index();

    if (log_nvm[index].magic == LOG_MAGIC)
    {
        rc = true;
    }

    log_load_entry(0);
    return rc;
}

// select: 0: newest; n-1: oldest
// globals vars must be initialized when this function is called
__attribute__((noinline)) void log_load_entry(uint16_t select)
{
    uint16_t index;

    index = UINT16_MAX;

    if (select < log_count)
    {
        index = log_index - select;

        if (index >= LOG_NVM_COUNT)     // wraparound -> adjust
        {
            index += LOG_NVM_COUNT;
        }
    }

    if (index < LOG_NVM_COUNT && log_nvm[index].magic == LOG_MAGIC)
    {
        log_if.date = log_nvm[index].log.date;
        log_if.status = log_nvm[index].log.status;
        strncpy(log_if.username, log_nvm[index].log.username, sizeof(log_if.username) - 1);
        log_if.username[sizeof(log_if.username) - 1] = '\0';
        log_if.count = log_count;
        log_if.select = select;
    }
    else
    {
        log_clear();
    }
}

__attribute__((noinline)) void log_write(uint32_t status)
{
    uint8_t index;
    log_nvm_t* p;
    static uint32_t rc __attribute__((unused));

    dbgp("%s(%x}: log idx=%u\n", __func__, status, log_index);
    index = log_index + 1;

    if (index >= LOG_NVM_COUNT)
    {
        index = 0;
    }

    p = (log_nvm_t*)&log_nvm[index];
    dbgp("p:%p d:%08x\n", p, *(uint32_t*)p);
    nvm_config();       // just to be sure
    rc = nvm_open_assembly_buffer((uint32_t)p);
    memset(p->log.username, 0xff, sizeof(p->log.username));
    strncpy(p->log.username, session.username, sizeof(p->log.username));

    if (p->log.username[sizeof(p->log.username) - 1] != '\0' && p->log.username[sizeof(p->log.username) - 2] == 0xff)
    {
        p->log.username[sizeof(p->log.username) - 1] = '\0';    // ensure end of string marker
    }

    // compiler optimizes the following line away:
    // p->log.date) = session.date;
    // ...so try this trick:
    volatile int64_t* p64 = &p->log.date;
    *p64 = session.date;

    p->log.status = status;
    p->seq = log_nvm[log_index].seq + 1;
    p->magic = LOG_MAGIC;
    nvm_program_page();
    nvm_config();
    log_index = index;

    if (log_count < LOG_NVM_COUNT)
    {
        log_count++;
        log_if.count = log_count;
    }

    dbgp("log idx:%u cnt:%u p:%p d:%08x\n", log_index, log_count, p, *(uint32_t*)p);

//#if defined DEBUG && DEBUG
//    session.date += 61;
//#endif
}


void nvm_clear(uint8_t erase)
{
    uint16_t index;

    if (erase & 0x01)           // lock key
    {
        index = 0;      // only 1st entry used

        if (user_cfg_nvm[index].magic == USER_CFG_MAGIC)
        {
            nvm_open_assembly_buffer((uint32_t)&user_cfg_nvm[index]);
            nvm_erase_page();
            nvm_config();
        }

        session.user_count = 0;
        dbgp("%s: lockkey erased\n", __func__);
    }

    if (erase & 0x02)           // log
    {
        for (index = 0; index < LOG_NVM_COUNT; index++)
        {
            if (log_nvm[index].magic == LOG_MAGIC)
            {
                nvm_open_assembly_buffer((uint32_t)&log_nvm[index]);
                nvm_erase_page();
                nvm_config();
            }
        }

        log_count = 0;
        log_if.count = log_count;
        dbgp("%s: log erased\n", __func__);
    }

    if (erase & 0x04)           // config
    {
        for (index = 0; index < CONFIG_NVM_COUNT; index++)
        {
            if (config_nvm[index].magic == CONFIG_MAGIC)
            {
                nvm_open_assembly_buffer((uint32_t)&config_nvm[index]);
                nvm_erase_page();
                nvm_config();
            }
        }

        config_set_default();
        dbgp("%s: config erased\n", __func__);
    }
}
