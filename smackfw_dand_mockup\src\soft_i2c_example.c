#include "soft_i2c_example.h"

static void my_i2c_delay(const uint32_t us)
{
  // 分段延时避免溢出
  for (uint32_t i = 0; i < us; i++)
  {
    // 每次延时1微秒
    volatile uint32_t loops = XTAL / (1000000UL * 5);
    if (loops < 1)
      loops = 1;

    while (loops--)
    {
      // __NOP();
    }
  }
}

static void my_sda_low(void)
{
  set_singlegpio_out(false, I2C_SDA_PIN);
}

static void my_sda_high(void)
{
  set_singlegpio_out(true, I2C_SDA_PIN);
}

static void my_scl_low(void)
{
  set_singlegpio_out(false, I2C_SCL_PIN);
}

static void my_scl_high(void)
{
  set_singlegpio_out(true, I2C_SCL_PIN);
}

static uint8_t my_sda_read(void)
{
  return get_singlegpio_in(I2C_SDA_PIN) ? 1 : 0;
}

static void my_sda_dir_input(void)
{
  single_gpio_iocfg(false, true, false, false, false, I2C_SDA_PIN);
}

static void my_sda_dir_output(void)
{
  single_gpio_iocfg(true, false, true, false, false, I2C_SDA_PIN);
}

static struct sf_i2c_dev my_i2c;

static void sensor_init_struct()
{
  my_i2c.name = "my_i2c";
  my_i2c.speed = 10;
  my_i2c.delay_us = my_i2c_delay;

  my_i2c.ops.sda_low = my_sda_low;
  my_i2c.ops.sda_high = my_sda_high;
  my_i2c.ops.scl_low = my_scl_low;
  my_i2c.ops.scl_high = my_scl_high;
  my_i2c.ops.sda_read_level = my_sda_read;
  my_i2c.ops.sda_set_input = my_sda_dir_input;
  my_i2c.ops.sda_set_output = my_sda_dir_output;
}

void sensor_init_sf()
{
  sensor_init_struct();

  single_gpio_iocfg(true, false, true, false, false, I2C_SDA_PIN);
  single_gpio_iocfg(true, false, true, false, false, I2C_SCL_PIN);

  sf_i2c_init(&my_i2c);

  my_i2c_delay(10000);

  // 配置传感器 - 参考Arduino示例的配置
  struct sf_i2c_msg msg[1];

  // 写入配置寄存器
  uint8_t config_buf[] = {SENSOR_CONFIG_REG, 0b00010001};
  msg[0].addr = SENSOR_ADDR << ADDRESS_SHIFT;
  msg[0].flags = SF_I2C_FLAG_WR;
  msg[0].buf = config_buf;
  msg[0].len = 2;
  sf_i2c_transfer(&my_i2c, msg, 1);

  // 写入MOD1寄存器
  uint8_t mod1_buf[] = {SENSOR_MOD1_REG, 0b10010001};
  msg[0].buf = mod1_buf;
  sf_i2c_transfer(&my_i2c, msg, 1);

  my_i2c_delay(10000);
}

void sensor_read_sf(int16_t *x, int16_t *y, int16_t *z, uint16_t *t)
{
  // 可能需要先触发测量
  struct sf_i2c_msg trigger_msg[1];
  uint8_t trigger_buf[] = {SENSOR_CONFIG_REG, 0b00010001}; // 重新触发测量
  trigger_msg[0].addr = SENSOR_ADDR << ADDRESS_SHIFT;
  trigger_msg[0].flags = SF_I2C_FLAG_WR;
  trigger_msg[0].buf = trigger_buf;
  trigger_msg[0].len = 2;
  sf_i2c_transfer(&my_i2c, trigger_msg, 1);

  my_i2c_delay(10000); // 等待测量完成

  // 然后读取数据
  struct sf_i2c_msg msg[1];
  uint8_t data[7] = {0};
  msg[0].addr = SENSOR_ADDR << ADDRESS_SHIFT;
  msg[0].flags = SF_I2C_FLAG_RD;
  msg[0].buf = data;
  msg[0].len = 7;
  sf_i2c_transfer(&my_i2c, msg, 1);

  // 解析数据...
  *x = (int16_t)((data[0] << 8) | (data[4] & 0xF0)) >> 4;
  *y = (int16_t)((data[1] << 8) | ((data[4] & 0x0F) << 4)) >> 4;
  *z = (int16_t)((data[2] << 8) | ((data[5] & 0x0F) << 4)) >> 4;
  *t = (data[3] << 4) | (data[5] >> 4);
}
