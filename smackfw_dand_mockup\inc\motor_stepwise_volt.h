/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     motor_stepwise_volt.h
 *
 * @brief    Smack motor control with voltage controlled stepwise configuration.
 *
 * @version  v1.0
 * @date     2021-11-26
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _MOTOR_STEPWISE_VOLT_H_
#define _MOTOR_STEPWISE_VOLT_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack
 * @{
 */


/** @addtogroup motor_control_stepwise_voltage
 * @{
 */

#include "smack_motor_if.h"


extern smack_motor_control_status_t motor_stepwise_volt_status;

extern const smack_motor_control_t motor_stepwise_volt_control;


/** @} */ /* End of group motor_control_stepwise_voltage */


/** @} */ /* End of group Smack */

/** @} */ /* End of group Infineon */

#endif /* _MOTOR_STEPWISE_VOLT_H_ */
