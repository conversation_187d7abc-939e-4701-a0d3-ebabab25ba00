/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     motor_common.h
 *
 * @brief    Smack motor control: common helper functions
 *
 * @version  v1.0
 * @date     2021-11-26
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _MOTOR_COMMON_H_
#define _MOTOR_COMMON_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack
 * @{
 */


/** @addtogroup motor_control_common
 * @{
 */


#undef wait_about_1ms
#define wait_about_1ms  (XTAL / 1000U)          // calculated from oscillator; more exact than rough estimate
#define ms2ticks(ms)    ((ms) * (wait_about_1ms))

// Lock analog unit (sense/compare) for use, e.g. switch on if not already done, increment user count
extern void sense_lock(void);
// Free analog unit, e.g. decrement user count, and switch off if not in use any more
extern void sense_free(void);


/** @} */ /* End of group motor_control_common */


/** @} */ /* End of group Smack */

/** @} */ /* End of group Infineon */

#endif /* _MOTOR_COMMON_H_ */
