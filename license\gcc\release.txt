Release notes for
****************************************************
GNU Arm Embedded Toolchain 2021.07
****************************************************

This release includes bare metal pre-built binaries for AArch32 EABI targets,
which can be hosted on:
* Windows 10 or later on IA-32 or x86_64
* Mac OS X 10.14 or later on x86_64
* Linux on x86_64
  These toolchains are built on and for Ubuntu 16.04 on x86_64,
  and will likely also be useable on OS versions:
  - later than Ubuntu 16.04
  - RHEL 7 or later
* Linux on AArch64
  These toolchains are built on and for Ubuntu 18.04 on AArch64,
  and will likely also be useable on OS versions:
  - later than Ubuntu 18.04
  - RHEL 8

For Windows, the binaries are provided with an installer and as a zip file.
For Linux, the binaries are provided as tarball files.
For Mac OS X, the binaries are provided as tarball and pkg files.

The release also contains source code package (together with build scripts and
instructions to setup the build environment), which is composed of:

  * gcc : refs/vendors/ARM/heads/arm-10
    git://gcc.gnu.org/git/gcc.git commit 098ef5a86d3547127be59aa0f8da7725f4121864

  * binutils : binutils-2_36-branch
    git://sourceware.org/git/binutils-gdb.git commit a7eb3ff36cebc093af6658049e03d63579dade86

  * newlib and newlib-nano : 2a3a03972b35377aef8d3d52d873ac3b8fcc512c
    git://sourceware.org/git/newlib-cygwin.git commit 2a3a03972b35377aef8d3d52d873ac3b8fcc512c

  * gdb : gdb-10-branch
    git://sourceware.org/git/binutils-gdb.git commit abbf09189720ddc14fe71d0afdfa7653b8c67b5d

Note that some or all of the following prerequisites are downloaded when
building from source:

  * EnvVarUpdate NSIS script :
    http://nsis.sourceforge.net/mediawiki/images/a/ad/EnvVarUpdate.7z

  * expat 2.1.1 :
    https://downloads.sourceforge.net/project/expat/expat/2.1.1/expat-2.1.1.tar.bz2

  * gmp 6.1.0 :
    https://gmplib.org/download/gmp/gmp-6.1.0.tar.bz2

  * isl 0.18 :
    http://isl.gforge.inria.fr/isl-0.18.tar.xz

  * libelf 0.8.13 :
    https://fossies.org/linux/misc/old/libelf-0.8.13.tar.gz

  * libiconv 1.15 :
    https://ftp.gnu.org/pub/gnu/libiconv/libiconv-1.15.tar.gz

  * mpc 1.0.3 :
    ftp://ftp.gnu.org/gnu/mpc/mpc-1.0.3.tar.gz

  * mpfr 3.1.4 :
    http://www.mpfr.org/mpfr-3.1.4/mpfr-3.1.4.tar.bz2

  * python 2.7.7 :
    https://www.python.org/ftp/python/2.7.7/python-2.7.7.msi

  * zlib 1.2.8 :
    http://www.zlib.net/fossils/zlib-1.2.8.tar.gz

Features:
* All GCC 10.3 features

Tests:
* Targets:
  + Variety of Cortex-M0/M0+/M3/M4/M7/A9 boards
  + Qemu
  + Arm Fast Models

Notable changes in 2021.07 release:
* Bumped binutils to version 2.36.1.

* Bumped glibc to version 2.33.

* Bumped newlib to a commit after version 4.1.0.

* Fixed the security vulnerability described in
  https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-3420.

* The toolchain executables for Windows host are signed with SHA2.

* Fixed issues https://gcc.gnu.org/bugzilla/show_bug.cgi?id=99157
  and https://gcc.gnu.org/bugzilla/show_bug.cgi?id=99939
  where CMSE support was missing from multilibs.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=100856
  where the correct multilibs were not linked when using the
  Custom Datapath Extension (+cdecp[0-7]) with the -march option.

* Fixed issue https://sourceware.org/bugzilla/show_bug.cgi?id=25235
  where forward-referencing ADR instructions were generating wrong
  offsets in Thumb code.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=99960
  where V2DImode loads and stores for MVE might be miscompiled.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=99725
  to avoid emitting CFA adjusts for CMSE nonsecure calls.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=95646
  where callee saved registers were cleared when using CMSE with -Os.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=99271
  where wrong code could be generated for armv8-m.main when
  calling __gnu_cmse_nonsecure_call.

* Fixed issue https://gcc.gnu.org/bugzilla/show_bug.cgi?id=101016
  where the linker was generating errors with undefined reference
  to "__ARM_undef" due to missing mve polymorphic variants.

* Fixed the security vulnerability described in
  https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-3420

Known issues:
* Doing IPA on CMSE generates a linker error:
The linker will error out when resulting object file contains a symbol for
the clone function with the __acle_se prefix that has a non-local binding.
  Issue occurs when compiling binaries for M-profile Secure Extensions where
the compiler may decide to clone a function with the cmse_nonsecure_entry
attribute.
  Although cloning nonsecure entry functions is legal, as long as the clone
is only used inside the secure application, the clone function itself should
not be seen as a secure entry point and so it should not have the __acle_se
prefix.
  A possible workaround for this is to add a 'noclone' attribute to
functions with the 'cmse_nonsecure_entry'. This will prevent GCC from cloning
such functions.

* GCC can hang or crash if the input source code uses MVE Intrinsics
polymorphic variants in a nested form. The depth of nesting that
triggers this issue might vary depending on the host machine.
This behaviour is observed when nesting 7 times or more on a high-end
workstation. On less powerful machines, this behaviour might be
observed with fewer levels of nesting. This issue is reported in
https://gcc.gnu.org/bugzilla/show_bug.cgi?id=91937
