/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_dand_nvm.h
 *
 * @brief    Smack dandelion test example file - NVM storage.
 *
 * @version  v1.0
 * @date     2021-10-01
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_DAND_NVM_H_
#define _SMACK_DAND_NVM_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack_dand_nvm
 * @{
 */


/* * @addtogroup fw_config
 * @{
 */


enum clamping_voltage_e
{
    vclamp_3000 = 3000,
    vclamp_3300 = 3300,
    vclamp_3600 = 3600,
    vclamp_rfu = 0,
};


enum motor_method_e
{
    motor_single = 1,
    motor_stepwise_voltage,
    motor_stepwise_timer,
    motor_simulation,
};


/**
 * @brief Load configuration from NVM into RAM
 *
 * @return true: success; false: configuration was not loaded, recent data is kept
 */
extern bool config_load(void);

/**
 * @brief Save configuration in NVM
 */
extern void config_save(void);

/**
 * @brief Set configuration data in RAM to defaults
 */
extern void config_set_default(void);


// session struct must already be initialized then loading the user configuration as session.user_count is updated.
extern void user_cfg_read(void);
extern void user_cfg_write(void);


extern bool log_load(void);
extern void log_load_entry(uint16_t select);
extern void log_write(uint32_t status);

// debug
// erase: bit field: bit0=erase lock key, bit1=erase log
extern void nvm_clear(uint8_t erase);


/* * @} */ /* End of group fw_config */


/** @} */ /* End of group Smack_dand_nvm */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_DAND_NVM_H_ */
