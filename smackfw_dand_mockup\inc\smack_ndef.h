/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_ndef.h
 *
 * @brief    Smack dandelion test example file.
 *
 * @version  v1.0
 * @date     2020-05-20
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_NDEF_H_
#define _SMACK_NDEF_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack_ndef
 * @{
 */


/** @addtogroup ndef_record
 * @{
 */


extern const uint8_t smack_sl_tag[];      /**< NDEF Tag2 area */


/** @} */ /* End of group ndef_record */


/** @} */ /* End of group Smack_ndef */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_NDEF_H_ */
