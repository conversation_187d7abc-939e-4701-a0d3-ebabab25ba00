<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.config.gnu.cross.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.cross.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="cdt.managedbuild.config.gnu.cross.exe.debug.**********" name="Debug" parent="cdt.managedbuild.config.gnu.cross.exe.debug">
					<folderInfo id="cdt.managedbuild.config.gnu.cross.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.cross.exe.debug.533567035" name="Cross GCC" superClass="cdt.managedbuild.toolchain.gnu.cross.exe.debug">
							<option id="cdt.managedbuild.option.gnu.cross.prefix.1202092479" name="Prefix" superClass="cdt.managedbuild.option.gnu.cross.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="cdt.managedbuild.option.gnu.cross.path.1716145346" name="Path" superClass="cdt.managedbuild.option.gnu.cross.path" value="${ProjDirPath}/../tools/extern/gcc-arm-none-eabi/bin" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.targetPlatform.gnu.cross.223908305" isAbstract="false" osList="all" superClass="cdt.managedbuild.targetPlatform.gnu.cross"/>
							<builder arguments="DEBUG=1" buildPath="" command="${ProjDirPath}/../scripts/eclipse_eval_make_errorlevel.bat" id="cdt.managedbuild.builder.gnu.cross.228629698" incrementalBuildTarget="all" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="false" superClass="cdt.managedbuild.builder.gnu.cross"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.c.compiler.**********" name="Cross GCC Compiler" superClass="cdt.managedbuild.tool.gnu.cross.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.c.compiler.option.optimization.level.1256660186" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.891914192" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.preprocessor.def.symbols.1821124803" name="Defined symbols (-D)" superClass="gnu.c.compiler.option.preprocessor.def.symbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_Static_assert(cond,message)"/>
									<listOptionValue builtIn="false" value="DEBUG=1"/>
								</option>
								<option id="gnu.c.compiler.option.include.paths.2028449353" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom/gen&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/ifx/smack_series/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/inc&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.821062201" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.cpp.compiler.671266909" name="Cross G++ Compiler" superClass="cdt.managedbuild.tool.gnu.cross.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.1921620895" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.604414492" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.c.linker.185332551" name="Cross GCC Linker" superClass="cdt.managedbuild.tool.gnu.cross.c.linker">
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.1488095853" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.cpp.linker.596577146" name="Cross G++ Linker" superClass="cdt.managedbuild.tool.gnu.cross.cpp.linker"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.archiver.1090186387" name="Cross GCC Archiver" superClass="cdt.managedbuild.tool.gnu.cross.archiver"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.assembler.643823512" name="Cross GCC Assembler" superClass="cdt.managedbuild.tool.gnu.cross.assembler">
								<option id="gnu.both.asm.option.include.paths.262146962" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom/gen&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/ifx/smack_series/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/inc&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.267831895" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="cdt.managedbuild.config.gnu.cross.exe.release.894432158">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.cross.exe.release.894432158" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" id="cdt.managedbuild.config.gnu.cross.exe.release.894432158" name="Release" parent="cdt.managedbuild.config.gnu.cross.exe.release">
					<folderInfo id="cdt.managedbuild.config.gnu.cross.exe.release.894432158." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.cross.exe.release.1134197717" name="Cross GCC" superClass="cdt.managedbuild.toolchain.gnu.cross.exe.release">
							<option id="cdt.managedbuild.option.gnu.cross.prefix.1822569619" name="Prefix" superClass="cdt.managedbuild.option.gnu.cross.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="cdt.managedbuild.option.gnu.cross.path.45000188" name="Path" superClass="cdt.managedbuild.option.gnu.cross.path" value="${ProjDirPath}/../tools/extern/gcc-arm-none-eabi/bin" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.targetPlatform.gnu.cross.1466976493" isAbstract="false" osList="all" superClass="cdt.managedbuild.targetPlatform.gnu.cross"/>
							<builder buildPath="" command="${ProjDirPath}/../scripts/eclipse_eval_make_errorlevel.bat" id="cdt.managedbuild.builder.gnu.cross.1381912280" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="cdt.managedbuild.builder.gnu.cross"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.c.compiler.929564540" name="Cross GCC Compiler" superClass="cdt.managedbuild.tool.gnu.cross.c.compiler">
								<option defaultValue="gnu.c.optimization.level.most" id="gnu.c.compiler.option.optimization.level.401163649" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.631568874" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.include.paths.1307301394" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom/gen&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/ifx/smack_series/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/inc&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.1193174042" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.cpp.compiler.1119809167" name="Cross G++ Compiler" superClass="cdt.managedbuild.tool.gnu.cross.cpp.compiler">
								<option id="gnu.cpp.compiler.option.optimization.level.2032612414" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.most" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.1630289120" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.none" valueType="enumerated"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.c.linker.1539747804" name="Cross GCC Linker" superClass="cdt.managedbuild.tool.gnu.cross.c.linker">
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.851271884" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cross.cpp.linker.1129262052" name="Cross G++ Linker" superClass="cdt.managedbuild.tool.gnu.cross.cpp.linker"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.archiver.1055107246" name="Cross GCC Archiver" superClass="cdt.managedbuild.tool.gnu.cross.archiver"/>
							<tool id="cdt.managedbuild.tool.gnu.cross.assembler.343759549" name="Cross GCC Assembler" superClass="cdt.managedbuild.tool.gnu.cross.assembler">
								<option id="gnu.both.asm.option.include.paths.1993812422" name="Include paths (-I)" superClass="gnu.both.asm.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom/gen&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/smack_lib/inc_rom&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/ifx/smack_series/inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../smack_rom/libs/CMSIS/inc&quot;"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.2088566115" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="smack_rom.cdt.managedbuild.target.gnu.cross.exe.1375256184" name="Executable" projectType="cdt.managedbuild.target.gnu.cross.exe"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.release.894432158;cdt.managedbuild.config.gnu.cross.exe.release.894432158.;cdt.managedbuild.tool.gnu.cross.c.compiler.929564540;cdt.managedbuild.tool.gnu.c.compiler.input.1193174042">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.**********;cdt.managedbuild.config.gnu.cross.exe.debug.**********.;cdt.managedbuild.tool.gnu.cross.c.compiler.**********;cdt.managedbuild.tool.gnu.c.compiler.input.821062201">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/smack_rom"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/smack_rom"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings">
		<doc-comment-owner id="org.eclipse.cdt.ui.doxygen">
			<path value=""/>
		</doc-comment-owner>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets">
		<buildTargets>
			<target name="test_protection" path="smack_app/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>test_protection</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test_system_init" path="smack_app/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>test_system_init</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="all" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>all</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="clean" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>clean</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="doc" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>doc</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="image_rom" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>scripts/eclipse_eval_make_errorlevel.bat</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>image_rom</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>test</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="clean_image" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>clean_image</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="clean_doc" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>clean_doc</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="doc_pdf_aim" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>scripts/eclipse_eval_make_errorlevel.bat</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>doc_pdf_aim</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="image_ram" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>scripts/eclipse_eval_make_errorlevel.bat</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>image_ram</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="package_rtlsim" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>scripts/eclipse_eval_make_errorlevel.bat</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>package_rtlsim</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="image_ram IGN_LINT=yes" path="" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>scripts/eclipse_eval_make_errorlevel.bat</buildCommand>
				<buildArguments>-j</buildArguments>
				<buildTarget>image_ram IGN_LINT=yes</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test_vco" path="Libraries/SmackLib/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j4</buildArguments>
				<buildTarget>test_vco</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test_leaky_bucket" path="Libraries/SmackLib/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>${ProjDirPath}/../tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j4</buildArguments>
				<buildTarget>test_leaky_bucket</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test_gb" path="Libraries/SmackLib/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j4</buildArguments>
				<buildTarget>test_gb</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
			<target name="test_protection" path="Libraries/SmackLib/test" targetID="org.eclipse.cdt.build.MakeTargetBuilder">
				<buildCommand>tools/extern/make/bin/make.exe</buildCommand>
				<buildArguments>-j4</buildArguments>
				<buildTarget>test_protection</buildTarget>
				<stopOnError>true</stopOnError>
				<useDefaultCommand>true</useDefaultCommand>
				<runAllBuilders>true</runAllBuilders>
			</target>
		</buildTargets>
	</storageModule>
</cproject>
