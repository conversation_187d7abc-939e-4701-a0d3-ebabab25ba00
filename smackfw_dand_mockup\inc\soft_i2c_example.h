#ifndef _SOFT_I2C_EXAMPLE_H_
#define _SOFT_I2C_EXAMPLE_H_

#include "soft_i2c.h"
#include "smack.h"
#include "rom_lib.h"
#include "settings.h"

#define I2C_SDA_PIN 1
#define I2C_SCL_PIN 0

#define ADDRESS_SHIFT 1

#define SENSOR_ADDR 0x35
#define SENSOR_CONFIG_REG 0x10
#define SENSOR_MOD1_REG 0x11
#define SENSOR_DATA_REG 0x00

void sensor_init_sf();
void sensor_read_sf(int16_t *x, int16_t *y, int16_t *z, uint16_t *t);

#endif
