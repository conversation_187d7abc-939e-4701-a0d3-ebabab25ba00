/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_motor_if.h
 *
 * @brief    Smack motor control interface definition.
 *
 * @version  v1.0
 * @date     2021-11-26
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_MOTOR_IF_H_
#define _SMACK_MOTOR_IF_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack
 * @{
 */


/** @addtogroup motor_control_interface
 * @{
 */


/**
 * @brief  motor control commands and status values.
 */
typedef enum smack_motor_command_e
{
    motor_ctrl_off      = 0,
    motor_ctrl_on       = 1,
    motor_ctrl_reverse  = 2,
} smack_motor_command_t;

typedef enum
{
    motor_status_unknown = 0,
    motor_status_pos1   = 1,    // unlocked
    motor_status_pos2   = 2,    // locked
    motor_status_moving = 4,
} smack_motor_status_t;


/**
 * @brief  some return values of motor control functions.
 */
enum
{
    rc_m_stop = 1,              // operation completed
    rc_m_again,                 // call state machine again without delay, next step must be performed as soon as possible
    rc_m_pause,                 // motor operation paused, CPU may enter a low power sleep until the next call to motor state machine
};


/**
 * @brief  Struct to hold control and status information (e.g. dynamic data) regarding motor control.
 *
 *         There is an instance provided by each motor control state machine
 */
typedef struct smack_motor_status_s
{
    uint32_t v_clamp;           // clamping voltage of power supply [mV] (-> motor)
    uint32_t t_total;           // total runtime of motor [ms] (-> motor)
    uint32_t param1;            // upper voltage limit [mV] or on time [ms] (-> motor)
    uint32_t param2;            // lower voltage limit [mV] or off time [ms] (-> motor)
    uint32_t charge;            // charging value (motor ->)
    uint32_t charge_threshold;  // "fully charged" value (motor ->)
    uint32_t progress;          // progress of motor operation (motor ->)
    uint32_t progress_threshold; // 100% value of progress (motor ->)
    smack_motor_status_t status; // status of motor operation
} smack_motor_control_status_t;

/**
 * @brief  Struct which defines the interface to motor control (constant data, e.g. function pointers).
 *
 *         There is an instance provided by each motor control state machine
 */
typedef int motor_rc_t;         // return value of most functions (>=0: some value; <0: error)
typedef struct smack_motor_control_s
{
    motor_rc_t (*init)(void);
    motor_rc_t (*exit)(void);
    motor_rc_t (*task)(void);
    motor_rc_t (*control)(smack_motor_command_t);
    motor_rc_t (*config)(void);
} smack_motor_control_t;

/**
 * @brief  Struct which defines the services provided by the firmware core to the motor control (constant data, e.g. access to timers).
 *
 *         There is one instance provided by the firmware core to the motor control state machine(s)
 */
typedef struct smack_motor_services_s
{
    uint32_t (*timer_get)(void);        // get timer value (for measuring charging times and motor run times)
} smack_motor_services_t;
extern const smack_motor_services_t smack_motor_services;


#define motor_func(control, func, ...) ((control && control->func) ? control->func(__VA_ARGS__) : (0))


/** @} */ /* End of group motor_control_interface */


/** @} */ /* End of group Smack */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_MOTOR_IF_H_ */
