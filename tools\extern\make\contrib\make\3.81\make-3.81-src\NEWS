GNU make NEWS                                               -*-indented-text-*-
  History of user-visible changes.
  1 April 2006

See the end of this file for copyrights and conditions.

All changes mentioned here are more fully described in the GNU make
manual, which is contained in this distribution as the file doc/make.texi.
See the README file and the GNU make manual for instructions for
reporting bugs.

Version 3.81

* GNU make is ported to OS/2.

* GNU make is ported to MinGW.  The MinGW build is only supported by
  the build_w32.bat batch file; see the file README.W32 for more
  details.

* WARNING: Future backward-incompatibility!
  Up to and including this release, the '$?' variable does not contain
  any prerequisite that does not exist, even though that prerequisite
  might have caused the target to rebuild.  Starting with the _next_
  release of GNU make, '$?' will contain all prerequisites that caused
  the target to be considered out of date.  See this Savannah bug:
  http://savannah.gnu.org/bugs/index.php?func=detailitem&item_id=16051

* WARNING: Backward-incompatibility!
  GNU make now implements a generic "second expansion" feature on the
  prerequisites of both explicit and implicit (pattern) rules.  In order
  to enable this feature, the special target '.SECONDEXPANSION' must be
  defined before the first target which takes advantage of it.  If this
  feature is enabled then after all rules have been parsed the
  prerequisites are expanded again, this time with all the automatic
  variables in scope.  This means that in addition to using standard
  SysV $$@ in prerequisites lists, you can also use complex functions
  such as $$(notdir $$@) etc.  This behavior applies to implicit rules,
  as well, where the second expansion occurs when the rule is matched.
  However, this means that when '.SECONDEXPANSION' is enabled you must
  double-quote any "$" in your filenames; instead of "foo: boo$$bar" you
  now must write "foo: foo$$$$bar".  Note that the SysV $$@ etc. feature,
  which used to be available by default, is now ONLY available when the
  .SECONDEXPANSION target is defined.  If your makefiles take advantage
  of this SysV feature you will need to update them.

* WARNING: Backward-incompatibility!
  In order to comply with POSIX, the way in which GNU make processes
  backslash-newline sequences in command strings has changed.  If your
  makefiles use backslash-newline sequences inside of single-quoted
  strings in command scripts you will be impacted by this change.  See
  the GNU make manual subsection "Splitting Command Lines" (node
  "Splitting Lines"), in section "Command Syntax", chapter "Writing the
  Commands in Rules", for details.

* WARNING: Backward-incompatibility!
  Some previous versions of GNU make had a bug where "#" in a function
  invocation such as $(shell ...) was treated as a make comment.  A
  workaround was to escape these with backslashes.  This bug has been
  fixed: if your makefile uses "\#" in a function invocation the
  backslash is now preserved, so you'll need to remove it.

* New command-line option: -L (--check-symlink-times).  On systems that
  support symbolic links, if this option is given then GNU make will
  use the most recent modification time of any symbolic links that are
  used to resolve target files.  The default behavior remains as it
  always has: use the modification time of the actual target file only.

* The "else" conditional line can now be followed by any other valid
  conditional on the same line: this does not increase the depth of the
  conditional nesting, so only one "endif" is required to close the
  conditional.

* All pattern-specific variables that match a given target are now used
  (previously only the first match was used).

* Target-specific variables can be marked as exportable using the
  "export" keyword.

* In a recursive $(call ...) context, any extra arguments from the outer
  call are now masked in the context of the inner call.

* Implemented a solution for the "thundering herd" problem with "-j -l".
  This version of GNU make uses an algorithm suggested by Thomas Riedl
  <<EMAIL>> to track the number of jobs started in the
  last second and artificially adjust GNU make's view of the system's
  load average accordingly.

* New special variables available in this release:
   - .INCLUDE_DIRS: Expands to a list of directories that make searches
     for included makefiles.
   - .FEATURES: Contains a list of special features available in this
     version of GNU make.
   - .DEFAULT_GOAL: Set the name of the default goal make will
     use if no goals are provided on the command line.
   - MAKE_RESTARTS: If set, then this is the number of times this
     instance of make has been restarted (see "How Makefiles Are Remade"
     in the manual).
   - New automatic variable: $| (added in 3.80, actually): contains all
     the order-only prerequisites defined for the target.

* New functions available in this release:
   - $(lastword ...) returns the last word in the list.  This gives
     identical results as $(word $(words ...) ...), but is much faster.
   - $(abspath ...) returns the absolute path (all "." and ".."
     directories resolved, and any duplicate "/" characters removed) for
     each path provided.
   - $(realpath ...) returns the canonical pathname for each path
     provided.  The canonical pathname is the absolute pathname, with
     all symbolic links resolved as well.
   - $(info ...) prints its arguments to stdout.  No makefile name or
     line number info, etc. is printed.
   - $(flavor ...) returns the flavor of a variable.
   - $(or ...) provides a short-circuiting OR conditional: each argument
     is expanded.  The first true (non-empty) argument is returned; no
     further arguments are expanded.  Expands to empty if there are no
     true arguments.
   - $(and ...) provides a short-circuiting AND conditional: each
     argument is expanded.  The first false (empty) argument is
     returned; no further arguments are expanded.  Expands to the last
     argument if all arguments are true.

* Changes made for POSIX compatibility:
   - Only touch targets (under -t) if they have at least one command.
   - Setting the SHELL make variable does NOT change the value of the
     SHELL environment variable given to programs invoked by make.  As
     an enhancement to POSIX, if you export the make variable SHELL then
     it will be set in the environment, just as before.

* On MS Windows systems, explicitly setting SHELL to a pathname ending
  in "cmd" or "cmd.exe" (case-insensitive) will force GNU make to use
  the DOS command interpreter in batch mode even if a UNIX-like shell
  could be found on the system.

* On VMS there is now support for case-sensitive filesystems such as ODS5.
  See the readme.vms file for information.

* Parallel builds (-jN) no longer require a working Bourne shell on
  Windows platforms.  They work even with the stock Windows shells, such
  as cmd.exe and command.com.

* Updated to autoconf 2.59, automake 1.9.5, and gettext 0.14.1.  Users
  should not be impacted.

* New translations for Swedish, Chinese (simplified), Ukrainian,
  Belarusian, Finnish, Kinyarwandan, and Irish.  Many updated
  translations.

A complete list of bugs fixed in this version is available here:

  http://savannah.gnu.org/bugs/index.php?group=make&report_id=111&fix_release_id=103


Version 3.80

* A new feature exists: order-only prerequisites.  These prerequisites
  affect the order in which targets are built, but they do not impact
  the rebuild/no-rebuild decision of their dependents.  That is to say,
  they allow you to require target B be built before target A, without
  requiring that target A will always be rebuilt if target B is updated.
  Patch for this feature provided by Greg McGary <<EMAIL>>.

* For compatibility with SysV make, GNU make now supports the peculiar
  syntax $$@, $$(@D), and $$(@F) in the prerequisites list of a rule.
  This syntax is only valid within explicit and static pattern rules: it
  cannot be used in implicit (suffix or pattern) rules.  Edouard G. Parmelan
  <<EMAIL>> provided a patch implementing this feature; however, I
  decided to implement it in a different way.

* The argument to the "ifdef" conditional is now expanded before it's
  tested, so it can be a constructed variable name.

  Similarly, the arguments to "export" (when not used in a variable
  definition context) and "unexport" are also now expanded.

* A new function is defined: $(value ...).  The argument to this
  function is the _name_ of a variable.  The result of the function is
  the value of the variable, without having been expanded.

* A new function is defined: $(eval ...).  The arguments to this
  function should expand to makefile commands, which will then be
  evaluated as if they had appeared in the makefile.  In combination
  with define/endef multiline variable definitions this is an extremely
  powerful capability.  The $(value ...) function is also sometimes
  useful here.

* A new built-in variable is defined, $(MAKEFILE_LIST).  It contains a
  list of each makefile GNU make has read, or started to read, in the
  order in which they were encountered.  So, the last filename in the
  list when a makefile is just being read (before any includes) is the
  name of the current makefile.

* A new built-in variable is defined: $(.VARIABLES).  When it is
  expanded it returns a complete list of variable names defined by all
  makefiles at that moment.

* A new command-line option is defined, -B or --always-make.  If
  specified GNU make will consider all targets out-of-date even if they
  would otherwise not be.

* The arguments to $(call ...) functions were being stored in $1, $2,
  etc. as recursive variables, even though they are fully expanded
  before assignment.  This means that escaped dollar signs ($$ etc.)
  were not behaving properly.  Now the arguments are stored as simple
  variables.  This may mean that if you added extra escaping to your
  $(call ...) function arguments you will need to undo it now.

* The variable invoked by $(call ...) can now be recursive: unlike other
  variables it can reference itself and this will not produce an error
  when it is used as the first argument to $(call ...) (but only then).

* New pseudo-target .LOW_RESOLUTION_TIME, superseding the configure
  option --disable-nsec-timestamps.  You might need this if your build
  process depends on tools like "cp -p" preserving time stamps, since
  "cp -p" (right now) doesn't preserve the subsecond portion of a time
  stamp.

* Updated translations for French, Galician, German, Japanese, Korean,
  and Russian.  New translations for Croatian, Danish, Hebrew, and
  Turkish.

* Updated internationalization support to Gettext 0.11.5.
  GNU make now uses Gettext's "external" feature, and does not include
  any internationalization code itself.  Configure will search your
  system for an existing implementation of GNU Gettext (only GNU Gettext
  is acceptable) and use it if it exists.  If not, NLS will be disabled.
  See ABOUT-NLS for more information.

* Updated to autoconf 2.54 and automake 1.7.  Users should not be impacted.

A complete list of bugs fixed in this version is available here:

  http://savannah.gnu.org/bugs/index.php?group=make&report_id=111&fix_release_id=102


Version 3.79.1

* .SECONDARY with no prerequisites now prevents any target from being
  removed because make thinks it's an intermediate file, not just those
  listed in the makefile.

* New configure option --disable-nsec-timestamps, but this was
  superseded in later versions by the .LOW_RESOLUTION_TIME pseudo-target.

Version 3.79

* GNU make optionally supports internationalization and locales via the
  GNU gettext (or local gettext if suitable) package.  See the ABOUT-NLS
  file for more information on configuring GNU make for NLS.

* Previously, GNU make quoted variables such as MAKEFLAGS and
  MAKEOVERRIDES for proper parsing by the shell.  This allowed them to
  be used within make build scripts.  However, using them there is not
  proper behavior: they are meant to be passed to subshells via the
  environment.  Unfortunately the values were not quoted properly to be
  passed through the environment.  This meant that make didn't properly
  pass some types of command line values to submakes.

  With this version we change that behavior: now these variables are
  quoted properly for passing through the environment, which is the
  correct way to do it.  If you previously used these variables
  explicitly within a make rule you may need to re-examine your use for
  correctness given this change.

* A new pseudo-target .NOTPARALLEL is available.  If defined, the
  current makefile is run serially regardless of the value of -j.
  However, submakes are still eligible for parallel execution.

* The --debug option has changed: it now allows optional flags
  controlling the amount and type of debugging output.  By default only
  a minimal amount information is generated, displaying the names of
  "normal" targets (not makefiles) that were deemed out of date and in
  need of being rebuilt.

  Note that the -d option behaves as before: it takes no arguments and
  all debugging information is generated.

* The `-p' (print database) output now includes filename and linenumber
  information for variable definitions, to aid debugging.

* The wordlist function no longer reverses its arguments if the "start"
  value is greater than the "end" value.  If that's true, nothing is
  returned.

* Hartmut Becker provided many updates for the VMS port of GNU make.
  See the readme.vms file for more details.

Version 3.78

* Two new functions, $(error ...) and $(warning ...) are available.  The
  former will cause make to fail and exit immediately upon expansion of
  the function, with the text provided as the error message.  The latter
  causes the text provided to be printed as a warning message, but make
  proceeds normally.

* A new function $(call ...) is available.  This allows users to create
  their own parameterized macros and invoke them later.  Original
  implementation of this function was provided by Han-Wen Nienhuys
  <<EMAIL>>.

* A new function $(if ...) is available.  It provides if-then-else
  capabilities in a builtin function.  Original implementation of this
  function was provided by Han-Wen Nienhuys <<EMAIL>>.

* Make defines a new variable, .LIBPATTERNS.  This variable controls how
  library dependency expansion (dependencies like ``-lfoo'') is performed.

* Make accepts CRLF sequences as well as traditional LF, for
  compatibility with makefiles created on other operating systems.

* Make accepts a new option: -R, or --no-builtin-variables.  This option
  disables the definition of the rule-specific builtin variables (CC,
  LD, AR, etc.).  Specifying this option forces -r (--no-builtin-rules)
  as well.

* A "job server" feature, suggested by Howard Chu <<EMAIL>>.

  On systems that support POSIX pipe(2) semantics, GNU make can now pass
  -jN options to submakes rather than forcing them all to use -j1.  The
  top make and all its sub-make processes use a pipe to communicate with
  each other to ensure that no more than N jobs are started across all
  makes.  To get the old behavior of -j back, you can configure make
  with the --disable-job-server option.

* The confusing term "dependency" has been replaced by the more accurate
  and standard term "prerequisite", both in the manual and in all GNU make
  output.

* GNU make supports the "big archive" library format introduced in AIX 4.3.

* GNU make supports large files on AIX, HP-UX, and IRIX.  These changes
  were provided by Paul Eggert <<EMAIL>>.  (Large file
  support for Solaris and Linux was introduced in 3.77, but the
  configuration had issues: these have also been resolved).

* The Windows 95/98/NT (W32) version of GNU make now has native support
  for the Cygnus Cygwin release B20.1 shell (bash).

* The GNU make regression test suite, long available separately "under
  the table", has been integrated into the release.  You can invoke it
  by running "make check" in the distribution.  Note that it requires
  Perl (either Perl 4 or Perl 5) to run.

Version 3.77

* Implement BSD make's "?=" variable assignment operator.  The variable
  is assigned the specified value only if that variable is not already
  defined.

* Make defines a new variable, "CURDIR", to contain the current working
  directory (after the -C option, if any, has been processed).
  Modifying this variable has no effect on the operation of make.

* Make defines a new default RCS rule, for new-style master file
  storage: ``% :: RCS/%'' (note no ``,v'' suffix).

  Make defines new default rules for DOS-style C++ file naming
  conventions, with ``.cpp'' suffixes.  All the same rules as for
  ``.cc'' and ``.C'' suffixes are provided, along with LINK.cpp and
  COMPILE.cpp macros (which default to the same value as LINK.cc and
  COMPILE.cc).  Note CPPFLAGS is still C preprocessor flags!  You should
  use CXXFLAGS to change C++ compiler flags.

* A new feature, "target-specific variable values", has been added.
  This is a large change so please see the appropriate sections of the
  manual for full details.  Briefly, syntax like this:

    TARGET: VARIABLE = VALUE

  defines VARIABLE as VALUE within the context of TARGET.  This is
  similar to SunOS make's "TARGET := VARIABLE = VALUE" feature.  Note
  that the assignment may be of any type, not just recursive, and that
  the override keyword is available.

  COMPATIBILITY: This new syntax means that if you have any rules where
  the first or second dependency has an equal sign (=) in its name,
  you'll have to escape them with a backslash: "foo : bar\=baz".
  Further, if you have any dependencies which already contain "\=",
  you'll have to escape both of them: "foo : bar\\\=baz".

* A new appendix listing the most common error and warning messages
  generated by GNU make, with some explanation, has been added to the
  GNU make User's Manual.

* Updates to the GNU make Customs library support (see README.customs).

* Updates to the Windows 95/NT port from Rob Tulloh (see README.W32),
  and to the DOS port from Eli Zaretski (see README.DOS).

Version 3.76.1

* Small (but serious) bug fix.  Quick rollout to get into the GNU source CD.

Version 3.76

* GNU make now uses automake to control Makefile.in generation.  This
  should make it more consistent with the GNU standards.

* VPATH functionality has been changed to incorporate the VPATH+ patch,
  previously maintained by Paul Smith <<EMAIL>>.  See the
  manual.

* Make defines a new variable, `MAKECMDGOALS', to contain the goals that
  were specified on the command line, if any.  Modifying this variable
  has no effect on the operation of make.

* A new function, `$(wordlist S,E,TEXT)', is available: it returns a
  list of words from number S to number E (inclusive) of TEXT.

* Instead of an error, detection of future modification times gives a
  warning and continues.  The warning is repeated just before GNU make
  exits, so it is less likely to be lost.

* Fix the $(basename) and $(suffix) functions so they only operate on
  the last filename, not the entire string:

      Command              Old Result             New Result
      -------              ----------             ----------
    $(basename a.b)        a                      a
    $(basename a.b/c)      a                      a.b/c
    $(suffix a.b)          b                      b
    $(suffix a.b/c)        b/c                    <empty>

* The $(strip) function now removes newlines as well as TABs and spaces.

* The $(shell) function now changes CRLF (\r\n) pairs to a space as well
  as newlines (\n).

* Updates to the Windows 95/NT port from Rob Tulloh (see README.W32).

* Eli Zaretskii has updated the port to 32-bit protected mode on MSDOS
  and MS-Windows, building with the DJGPP v2 port of GNU C/C++ compiler
  and utilities.  See README.DOS for details, and direct all questions
  concerning this port to Eli Zaretskii <<EMAIL>> or DJ
  Delorie <<EMAIL>>.

* John W. Eaton has updated the VMS port to support libraries and VPATH.

Version 3.75

* The directory messages printed by `-w' and implicitly in sub-makes,
  are now omitted if Make runs no commands and has no other messages to print.

* Make now detects files that for whatever reason have modification times
  in the future and gives an error.  Files with such impossible timestamps
  can result from unsynchronized clocks, or archived distributions
  containing bogus timestamps; they confuse Make's dependency engine
  thoroughly.

* The new directive `sinclude' is now recognized as another name for
  `-include', for compatibility with some other Makes.

* Aaron Digulla has contributed a port to AmigaDOS.  See README.Amiga for
  details, and direct all Amiga-related questions to <<EMAIL>>.

* Rob Tulloh of Tivoli Systems has contributed a port to Windows NT or 95.
  See README.W32 for details, and direct all Windows-related questions to
  <<EMAIL>>.

Version 3.73

* Converted to use Autoconf version 2, so `configure' has some new options.
  See INSTALL for details.

* You can now send a SIGUSR1 signal to Make to toggle printing of debugging
  output enabled by -d, at any time during the run.

Version 3.72

* DJ Delorie has ported Make to MS-DOS using the GO32 extender.
  He is maintaining the DOS port, not the GNU Make maintainer;
  please direct bugs and questions for DOS to <<EMAIL>>.
  MS-DOS binaries are available for FTP from ftp.simtel.net in
  /pub/simtelnet/gnu/djgpp/.

* The `MAKEFLAGS' variable (in the environment or in a makefile) can now
  contain variable definitions itself; these are treated just like
  command-line variable definitions.  Make will automatically insert any
  variable definitions from the environment value of `MAKEFLAGS' or from
  the command line, into the `MAKEFLAGS' value exported to children.  The
  `MAKEOVERRIDES' variable previously included in the value of `$(MAKE)'
  for sub-makes is now included in `MAKEFLAGS' instead.  As before, you can
  reset `MAKEOVERRIDES' in your makefile to avoid putting all the variables
  in the environment when its size is limited.

* If `.DELETE_ON_ERROR' appears as a target, Make will delete the target of
  a rule if it has changed when its commands exit with a nonzero status,
  just as when the commands get a signal.

* The automatic variable `$+' is new.  It lists all the dependencies like
  `$^', but preserves duplicates listed in the makefile.  This is useful
  for linking rules, where library files sometimes need to be listed twice
  in the link order.

* You can now specify the `.IGNORE' and `.SILENT' special targets with
  dependencies to limit their effects to those files.  If a file appears as
  a dependency of `.IGNORE', then errors will be ignored while running the
  commands to update that file.  Likewise if a file appears as a dependency
  of `.SILENT', then the commands to update that file will not be printed
  before they are run.  (This change was made to conform to POSIX.2.)

Version 3.71

* The automatic variables `$(@D)', `$(%D)', `$(*D)', `$(<D)', `$(?D)', and
  `$(^D)' now omit the trailing slash from the directory name.  (This change
  was made to comply with POSIX.2.)

* The source distribution now includes the Info files for the Make manual.
  There is no longer a separate distribution containing Info and DVI files.

* You can now set the variables `binprefix' and/or `manprefix' in
  Makefile.in (or on the command line when installing) to install GNU make
  under a name other than `make' (i.e., ``make binprefix=g install''
  installs GNU make as `gmake').

* The built-in Texinfo rules use the new variables `TEXI2DVI_FLAGS' for
  flags to the `texi2dvi' script, and `MAKEINFO_FLAGS' for flags to the
  Makeinfo program.

* The exit status of Make when it runs into errors is now 2 instead of 1.
  The exit status is 1 only when using -q and some target is not up to date.
  (This change was made to comply with POSIX.2.)

Version 3.70

* It is no longer a fatal error to have a NUL character in a makefile.
  You should never put a NUL in a makefile because it can have strange
  results, but otherwise empty lines full of NULs (such as produced by
  the `xmkmf' program) will always work fine.

* The error messages for nonexistent included makefiles now refer to the
  makefile name and line number where the `include' appeared, so Emacs's
  C-x ` command takes you there (in case it's a typo you need to fix).

Version 3.69

* Implicit rule search for archive member references is now done in the
  opposite order from previous versions: the whole target name `LIB(MEM)'
  first, and just the member name and parentheses `(MEM)' second.

* Make now gives an error for an unterminated variable or function reference.
  For example, `$(foo' with no matching `)' or `${bar' with no matching `}'.

* The new default variable `MAKE_VERSION' gives the version number of
  Make, and a string describing the remote job support compiled in (if any).
  Thus the value (in this release) is something like `3.69' or `3.69-Customs'.

* Commands in an invocation of the `shell' function are no longer run with
  a modified environment like target commands are.  As in versions before
  3.68, they now run with the environment that `make' started with.  We
  have reversed the change made in version 3.68 because it turned out to
  cause a paradoxical situation in cases like:

	export variable = $(shell echo value)

  When Make attempted to put this variable in the environment for a target
  command, it would try expand the value by running the shell command
  `echo value'.  In version 3.68, because it constructed an environment
  for that shell command in the same way, Make would begin to go into an
  infinite loop and then get a fatal error when it detected the loop.

* The commands given for `.DEFAULT' are now used for phony targets with no
  commands.

Version 3.68

* You can list several archive member names inside parenthesis:
  `lib(mem1 mem2 mem3)' is equivalent to `lib(mem1) lib(mem2) lib(mem3)'.

* You can use wildcards inside archive member references.  For example,
  `lib(*.o)' expands to all existing members of `lib' whose names end in
  `.o' (e.g. `lib(a.o) lib(b.o)'); `*.a(*.o)' expands to all such members
  of all existing files whose names end in `.a' (e.g. `foo.a(a.o)
  foo.a(b.o) bar.a(c.o) bar.a(d.o)'.

* A suffix rule `.X.a' now produces two pattern rules:
	(%.o): %.X	# Previous versions produced only this.
	%.a: %.X	# Now produces this as well, just like other suffixes.

* The new flag `--warn-undefined-variables' says to issue a warning message
  whenever Make expands a reference to an undefined variable.

* The new `-include' directive is just like `include' except that there is
  no error (not even a warning) for a nonexistent makefile.

* Commands in an invocation of the `shell' function are now run with a
  modified environment like target commands are, so you can use `export' et
  al to set up variables for them.  They used to run with the environment
  that `make' started with.

Version 3.66

* `make --version' (or `make -v') now exits immediately after printing
  the version number.

Version 3.65

* Make now supports long-named members in `ar' archive files.

Version 3.64

* Make now supports the `+=' syntax for a variable definition which appends
  to the variable's previous value.  See the section `Appending More Text
  to Variables' in the manual for full details.

* The new option `--no-print-directory' inhibits the `-w' or
  `--print-directory' feature.  Make turns on `--print-directory'
  automatically if you use `-C' or `--directory', and in sub-makes; some
  users have found this behavior undesirable.

* The built-in implicit rules now support the alternative extension
  `.txinfo' for Texinfo files, just like `.texinfo' and `.texi'.

Version 3.63

* Make now uses a standard GNU `configure' script.  See the new file
  INSTALL for the new (and much simpler) installation procedure.

* There is now a shell script to build Make the first time, if you have no
  other `make' program.  `build.sh' is created by `configure'; see README.

* GNU Make now completely conforms to the POSIX.2 specification for `make'.

* Elements of the `$^' and `$?' automatic variables that are archive
  member references now list only the member name, as in Unix and POSIX.2.

* You should no longer ever need to specify the `-w' switch, which prints
  the current directory before and after Make runs.  The `-C' switch to
  change directory, and recursive use of Make, now set `-w' automatically.

* Multiple double-colon rules for the same target will no longer have their
  commands run simultaneously under -j, as this could result in the two
  commands trying to change the file at the same time and interfering with
  one another.

* The `SHELL' variable is now never taken from the environment.
  Each makefile that wants a shell other than the default (/bin/sh) must
  set SHELL itself.  SHELL is always exported to child processes.
  This change was made for compatibility with POSIX.2.

* Make now accepts long options.  There is now an informative usage message
  that tells you what all the options are and what they do.  Try `make --help'.

* There are two new directives: `export' and `unexport'.  All variables are
  no longer automatically put into the environments of the commands that
  Make runs.  Instead, only variables specified on the command line or in
  the environment are exported by default.  To export others, use:
	export VARIABLE
  or you can define variables with:
	export VARIABLE = VALUE
  or:
	export VARIABLE := VALUE
  You can use just:
	export
  or:
	.EXPORT_ALL_VARIABLES:
  to get the old behavior.  See the node `Variables/Recursion' in the manual
  for a full description.

* The commands from the `.DEFAULT' special target are only applied to
  targets which have no rules at all, not all targets with no commands.
  This change was made for compatibility with Unix make.

* All fatal error messages now contain `***', so they are easy to find in
  compilation logs.

* Dependency file names like `-lNAME' are now replaced with the actual file
  name found, as with files found by normal directory search (VPATH).
  The library file `libNAME.a' may now be found in the current directory,
  which is checked before VPATH; the standard set of directories (/lib,
  /usr/lib, /usr/local/lib) is now checked last.
  See the node `Libraries/Search' in the manual for full details.

* A single `include' directive can now specify more than one makefile to
  include, like this:
	include file1 file2
  You can also use shell file name patterns in an `include' directive:
	include *.mk

* The default directories to search for included makefiles, and for
  libraries specified with `-lNAME', are now set by configuration.

* You can now use blanks as well as colons to separate the directories in a
  search path for the `vpath' directive or the `VPATH' variable.

* You can now use variables and functions in the left hand side of a
  variable assignment, as in "$(foo)bar = value".

* The `MAKE' variable is always defined as `$(MAKE_COMMAND) $(MAKEOVERRIDES)'.
  The `MAKE_COMMAND' variable is now defined to the name with which make
  was invoked.

* The built-in rules for C++ compilation now use the variables `$(CXX)' and
  `$(CXXFLAGS)' instead of `$(C++)' and `$(C++FLAGS)'.  The old names had
  problems with shells that cannot have `+' in environment variable names.

* The value of a recursively expanded variable is now expanded when putting
  it into the environment for child processes.  This change was made for
  compatibility with Unix make.

* A rule with no targets before the `:' is now accepted and ignored.
  This change was made for compatibility with SunOS 4 make.
  We do not recommend that you write your makefiles to take advantage of this.

* The `-I' switch can now be used in MAKEFLAGS, and are put there
  automatically just like other switches.

Version 3.61

* Built-in rules for C++ source files with the `.C' suffix.
  We still recommend that you use `.cc' instead.

* If commands are given too many times for a single target,
  the last set given is used, and a warning message is printed.

* Error messages about makefiles are in standard GNU error format,
  so C-x ` in Emacs works on them.

* Dependencies of pattern rules which contain no % need not actually exist
  if they can be created (just like dependencies which do have a %).

Version 3.60

* A message is always printed when Make decides there is nothing to be done.
  It used to be that no message was printed for top-level phony targets
  (because "`phony' is up to date" isn't quite right).  Now a different
  message "Nothing to be done for `phony'" is printed in that case.

* Archives on AIX now supposedly work.

* When the commands specified for .DEFAULT are used to update a target,
  the $< automatic variable is given the same value as $@ for that target.
  This is how Unix make behaves, and this behavior is mandated by POSIX.2.

Version 3.59

* The -n, -q, and -t options are not put in the `MAKEFLAGS' and `MFLAG'
  variables while remaking makefiles, so recursive makes done while remaking
  makefiles will behave properly.

* If the special target `.NOEXPORT' is specified in a makefile,
  only variables that came from the environment and variables
  defined on the command line are exported.

Version 3.58

* Suffix rules may have dependencies (which are ignored).

Version 3.57

* Dependencies of the form `-lLIB' are searched for as /usr/local/lib/libLIB.a
  as well as libLIB.a in /usr/lib, /lib, the current directory, and VPATH.

Version 3.55

* There is now a Unix man page for GNU Make.  It is certainly not a replacement
for the Texinfo manual, but it documents the basic functionality and the
switches.  For full documentation, you should still read the Texinfo manual.
Thanks to Dennis Morse of Stanford University for contributing the initial
version of this.

* Variables which are defined by default (e.g., `CC') will no longer be put
into the environment for child processes.  (If these variables are reset by the
environment, makefiles, or the command line, they will still go into the
environment.)

* Makefiles which have commands but no dependencies (and thus are always
  considered out of date and in need of remaking), will not be remade (if they
  were being remade only because they were makefiles).  This means that GNU
  Make will no longer go into an infinite loop when fed the makefiles that
  `imake' (necessary to build X Windows) produces.

* There is no longer a warning for using the `vpath' directive with an explicit
pathname (instead of a `%' pattern).

Version 3.51

* When removing intermediate files, only one `rm' command line is printed,
listing all file names.

* There are now automatic variables `$(^D)', `$(^F)', `$(?D)', and `$(?F)'.
These are the directory-only and file-only versions of `$^' and `$?'.

* Library dependencies given as `-lNAME' will use "libNAME.a" in the current
directory if it exists.

* The automatic variable `$($/)' is no longer defined.

* Leading `+' characters on a command line make that line be executed even
under -n, -t, or -q (as if the line contained `$(MAKE)').

* For command lines containing `$(MAKE)', `${MAKE}', or leading `+' characters,
only those lines are executed, not their entire rules.
(This is how Unix make behaves for lines containing `$(MAKE)' or `${MAKE}'.)

Version 3.50

* Filenames in rules will now have ~ and ~USER expanded.

* The `-p' output has been changed so it can be used as a makefile.
(All information that isn't specified by makefiles is prefaced with comment
characters.)

Version 3.49

* The % character can be quoted with backslash in implicit pattern rules,
static pattern rules, `vpath' directives, and `patsubst', `filter', and
`filter-out' functions.  A warning is issued if a `vpath' directive's
pattern contains no %.

* The `wildcard' variable expansion function now expands ~ and ~USER.

* Messages indicating failed commands now contain the target name:
	make: *** [target] Error 1

* The `-p' output format has been changed somewhat to look more like
makefile rules and to give all information that Make has about files.

Version 3.48

Version 3.47

* The `-l' switch with no argument removes any previous load-average limit.

* When the `-w' switch is in effect, and Make has updated makefiles,
it will write a `Leaving directory' messagfe before re-executing itself.
This makes the `directory change tracking' changes to Emacs's compilation
commands work properly.

Version 3.46

* The automatic variable `$*' is now defined for explicit rules,
as it is in Unix make.

Version 3.45

* The `-j' switch is now put in the MAKEFLAGS and MFLAGS variables when
specified without an argument (indicating infinite jobs).
The `-l' switch is not always put in the MAKEFLAGS and MFLAGS variables.

* Make no longer checks hashed directories after running commands.
The behavior implemented in 3.41 caused too much slowdown.

Version 3.44

* A dependency is NOT considered newer than its dependent if
they have the same modification time.  The behavior implemented
in 3.43 conflicts with RCS.

Version 3.43

* Dependency loops are no longer fatal errors.

* A dependency is considered newer than its dependent if
they have the same modification time.

Version 3.42

* The variables F77 and F77FLAGS are now set by default to $(FC) and
$(FFLAGS).  Makefiles designed for System V make may use these variables in
explicit rules and expect them to be set.  Unfortunately, there is no way to
make setting these affect the Fortran implicit rules unless FC and FFLAGS
are not used (and these are used by BSD make).

Version 3.41

* Make now checks to see if its hashed directories are changed by commands.
Other makes that hash directories (Sun, 4.3 BSD) don't do this.

Version 3.39

* The `shell' function no longer captures standard error output.

Version 3.32

* A file beginning with a dot can be the default target if it also contains
a slash (e.g., `../bin/foo').  (Unix make allows this as well.)

Version 3.31

* Archive member names are truncated to 15 characters.

* Yet more USG stuff.

* Minimal support for Microport System V (a 16-bit machine and a
brain-damaged compiler).  This has even lower priority than other USG
support, so if it gets beyond trivial, I will take it out completely.

* Revamped default implicit rules (not much visible change).

* The -d and -p options can come from the environment.

Version 3.30

* Improved support for USG and HPUX (hopefully).

* A variable reference like `$(foo:a=b)', if `a' contains a `%', is
equivalent to `$(patsubst a,b,$(foo))'.

* Defining .DEFAULT with no deps or commands clears its commands.

* New default implicit rules for .S (cpp, then as), and .sh (copy and make
executable).  All default implicit rules that use cpp (even indirectly), use
$(CPPFLAGS).

Version 3.29

* Giving the -j option with no arguments gives you infinite jobs.

Version 3.28

* New option: "-l LOAD" says not to start any new jobs while others are
running if the load average is not below LOAD (a floating-point number).

* There is support in place for implementations of remote command execution
in Make.  See the file remote.c.

Version 3.26

* No more than 10 directories will be kept open at once.
(This number can be changed by redefining MAX_OPEN_DIRECTORIES in dir.c.)

Version 3.25

* Archive files will have their modification times recorded before doing
anything that might change their modification times by updating an archive
member.

Version 3.20

* The `MAKELEVEL' variable is defined for use by makefiles.

Version 3.19

* The recursion level indications in error messages are much shorter than
they were in version 3.14.

Version 3.18

* Leading spaces before directives are ignored (as documented).

* Included makefiles can determine the default goal target.
(System V Make does it this way, so we are being compatible).

Version 3.14.

* Variables that are defaults built into Make will not be put in the
environment for children.  This just saves some environment space and,
except under -e, will be transparent to sub-makes.

* Error messages from sub-makes will indicate the level of recursion.

* Hopefully some speed-up for large directories due to a change in the
directory hashing scheme.

* One child will always get a standard input that is usable.

* Default makefiles that don't exist will be remade and read in.

Version 3.13.

* Count parentheses inside expansion function calls so you can
have nested calls: `$(sort $(foreach x,a b,$(x)))'.

Version 3.12.

* Several bug fixes, including USG and Sun386i support.

* `shell' function to expand shell commands a la `

* If the `-d' flag is given, version information will be printed.

* The `-c' option has been renamed to `-C' for compatibility with tar.

* The `-p' option no longer inhibits other normal operation.

* Makefiles will be updated and re-read if necessary.

* Can now run several commands at once (parallelism), -j option.

* Error messages will contain the level of Make recursion, if any.

* The `MAKEFLAGS' and `MFLAGS' variables will be scanned for options after
makefiles are read.

* A double-colon rule with no dependencies will always have its commands run.
(This is how both the BSD and System V versions of Make do it.)

Version 3.05

(Changes from versions 1 through 3.05 were never recorded.  Sorry.)

-------------------------------------------------------------------------------
Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997,
1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006 Free Software
Foundation, Inc.
This file is part of GNU Make.

GNU Make is free software; you can redistribute it and/or modify it under the
terms of the GNU General Public License as published by the Free Software
Foundation; either version 2, or (at your option) any later version.

GNU Make is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
A PARTICULAR PURPOSE.  See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with
GNU Make; see the file COPYING.  If not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
