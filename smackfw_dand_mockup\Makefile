# ============================================================================
# Copyright (c) 2021 Infineon Technologies AG
#               All rights reserved.
#               www.infineon.com
# ============================================================================
#
# ============================================================================
# Redistribution and use of this software only permitted to the extent
# expressly agreed with Infineon Technologies AG.
# ============================================================================

###################################################################################################
# Variables
###################################################################################################
REPO_ROOT_DIR := $(abspath ../)
PROJECT_ROOT_DIR := $(abspath ./)
BUILD_DIR := $(PROJECT_ROOT_DIR)/build

# Linker configuration script
LINKER_CONFIG_FILE := $(PROJECT_ROOT_DIR)/src/Linker_config.ld

# Source code directories: Where does the build process scan for '*.c' and '*.S' files ...
# For this project, we consider only the 'src' folder in the project root. This is where
# all the self-contained sources like test_example1.c sit.
PROJECT_SOURCE_DIRS := \
    $(PROJECT_ROOT_DIR)/src

# In case the source code uses stuff from Smack library (i.e., the 'ROM code'),
# we obviously want to tell the linker that it considers the 'ROM code' as a
# library and links against it.
# Obviously, this requires that the ROM code has been built ...
LIBRARY_DIRS := $(REPO_ROOT_DIR)/smack_rom/build/image/objects

# Smack ROM image libary
# Ok, this is worth being explained:
# gcc linker has an option '-l' which allows to specify libraries to link against, see
# e.g. LINKER_LIBS_PARAMS in rtlverification.mk. '-l' assumes 'lib' as the name of the lib
# plus some descriptive name and the file extension '.a'.
# The descriptive name in our case is LIBRARY_FILES ...
# So, all in all: 'lib_smack.a' is the library that is specified here and that will be used
# when building the executables.
# Note: the library we link against is not necessarily generated by the FW that is currently
#       under development. Consequently, it does not make sense to refer to a common variable 
#       for the name. Each project has it's own setting for the library.
# LIBRARY_FILES := _smack

# libraries to link
LINKER_LIBRARIES := smack
# smack1
ifdef DEBUG
ifneq ($(DEBUG),0)
LINKER_LIBRARIES += smack_dbg smack_extern
endif
endif

# ...and the directories to search for them
NVM_LIB_DIRS := $(sort $(wildcard $(REPO_ROOT_DIR)/smack_lib*))
NVM_LIB_BUILD_DIRS := $(addsuffix /build/image/, $(NVM_LIB_DIRS))

NVM_LIB_HEADER_DIRS := $(sort $(wildcard $(REPO_ROOT_DIR)/smack_lib*/inc))

PROJECT_HEADER_DIRS := \
    $(PROJECT_ROOT_DIR) \
    $(PROJECT_ROOT_DIR)/inc/ \
    $(PROJECT_ROOT_DIR)/src/support/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/smack_lib/doc/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/smack_lib/inc/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/smack_lib/inc_rom/gen/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/smack_lib/inc_rom/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/CMSIS/ifx/smack_series/inc/ \
    $(REPO_ROOT_DIR)/smack_rom/libs/CMSIS/inc/ \
    $(NVM_LIB_HEADER_DIRS)

DOCGENERATOR_EXTERNAL_SOURCE_DIRS := \
    $(PROJECT_ROOT_DIR)/doc/smack_main_doxy.h \
    $(REPO_ROOT_DIR)/smack_rom/libs/CMSIS/ifx/smack_series/inc \
    $(REPO_ROOT_DIR)/smack_rom/libs/CMSIS/inc

CCOMPILER_DIRS := $(filter-out $(CCOMPILER_DIRS_FILTER), $(PROJECT_SOURCE_DIRS))

# Linting is done on all source files of this project ..
CCODEANALYZER_SOURCE_DIRS := $(PROJECT_SOURCE_DIRS)

# Linting is done using all the header of this project.
# There is one exception: All the CMSIS headers (from ARM) are a) neither our
# responsibility nor b) can/should be changed by us. But linting them creates
# lots of errors. So, we replace them by a linting-clean version. Technically, this
# the same as when dealing with stdxx.h header files (e.g. stdint.h etc.): Lint 
# fails when linting the gcc-provided ones, so they are replaced, see lint/ansi
CCODEANALYZER_HEADER_FILTER := $(REPO_ROOT_DIR)/smack_rom/libs/CMSIS/inc/
CCODEANALYZER_HEADER_DIRS := $(filter-out $(CCODEANALYZER_HEADER_FILTER), $(PROJECT_HEADER_DIRS))
CCODEANALYZER_HEADER_DIRS += $(REPO_ROOT_DIR)/tool_config/lint/cmsis/

LINKER_CONFIG_ROM_FILE := $(PROJECT_ROOT_DIR)/src/Linker_config.ld
LINKER_CONFIG_NVM_FILE := $(PROJECT_ROOT_DIR)/src/Linker_config.ld

LINKER_INCLUDE_DIRS += \
	$(REPO_ROOT_DIR)/smack_rom/libs/smack_lib/inc/ \
	$(NVM_LIB_BUILD_DIRS)

# link the flash code against the ROM image
ROM_REFERENCE_IMAGE := $(REPO_ROOT_DIR)/smack_rom/build/image/image_rom.elf

# remove library functions not called from binary
LINKER_PARAMS += -Wl,--gc-sections

ifneq ($(ROM_REFERENCE_IMAGE), ) # if empty
# flash code is linked against the ROM code image
LINKER_PARAMS += -Wl,--just-symbols,$(ROM_REFERENCE_IMAGE)
endif

# link the flash code against the DPARAM image
DPARAM_REFERENCE_IMAGE := $(REPO_ROOT_DIR)/smack_rom/build/dparams/default_dparam.elf

ifneq ($(DPARAM_REFERENCE_IMAGE), ) # if empty
# flash code is linked against the DPARAM code image
LINKER_PARAMS += -Wl,--just-symbols,$(DPARAM_REFERENCE_IMAGE)
endif


# Name of the generated xml file that contains the structural information of 
# production_inst object in RAM
#PRODUCTION_INST_NAME := production_inst
#PRODUCTION_INST_TYPE := Production_Inst_t

# Include the build makefile environment for NVM code
include $(REPO_ROOT_DIR)/tools/intern/make/imagebuild.mk
include $(REPO_ROOT_DIR)/tools/intern/make/dox.mk

###################################################################################################
# Targets
###################################################################################################

# Create a package of FW image and FW sources and move it 
# to Unix to allow RTL simulation of the FW
# 
# To allow tracking of what FW is 'actually' being simulated over in the Unix/EDA world, we
# pass the git commit ID and the git status over to Unix.
package_rtlsim: image_nvm
	rm -rf commit_id
	git status
	git log -n 1 --format=format:"FW version from %%ad" HEAD > $(PROJECT_ROOT_DIR)/commit_id
	echo . >> $(PROJECT_ROOT_DIR)/commit_id
	git log -n 1 --format=format:"%%H" HEAD >> $(PROJECT_ROOT_DIR)/commit_id
	$(REPO_ROOT_DIR)/scripts/package_rtlsim_create.bat $(PROJECT_ROOT_DIR) smack_sl

help: test_help
.PHONY: test_help
test_help:
	@$(ECHO) 'make test             build and execute unit tests in $(PROJECT_ROOT_DIR)/test

# Unit testing 'clean' is encapsulated in a separate Makefile
# "test_clean" disabled - will not work with SDK
#clean: test_clean
test_clean:
	$(MAKE) -C $(PROJECT_ROOT_DIR)/test clean

test: test_all
test_all: tools
	$(MAKE) -C $(PROJECT_ROOT_DIR)/test all

all: image_nvm
