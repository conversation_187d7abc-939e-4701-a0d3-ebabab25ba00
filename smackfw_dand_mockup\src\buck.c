#include "buck.h"

void buck_init() {
    // GPIO4 输出低电平
    // Buck enable: output, buck switched off
    set_singlegpio_alt(GPIO_BUCK_EN, 0, 0);
    set_singlegpio_out(0, GPIO_BUCK_EN);
    single_gpio_iocfg(true, false, true, false, false, GPIO_BUCK_EN);

    // GPIO3 设置上拉输入
    // charge detect: input, enable internal pullup
    set_singlegpio_alt(GPIO_CHARGE_DETECT, 0, 0);
    single_gpio_iocfg(false, true, true, true, false, GPIO_CHARGE_DETECT);

    // GPIO1 配置 PWM 输出
    // PWM output to control booster switch
    // Set the PWM timing but do not start the timer
    set_singlegpio_alt(GPIO_PWM, 0, 3);         // select PWM output alternative
    sys_tim_pwm_config(PWM_PERIOD_TICKS - 1, PWM_DUTY_TICKS);   // configure PWM
    single_gpio_iocfg(true, false, true, false, false, GPIO_PWM);       // enable output driver on GPIO which is configured to PWM function
}

void buck_process() {
  // 预先检测 GPIO3，如果是低电平就跳过 PWM
  int gpio_charge_detect_complete_count = 0;
  for (int t = 0; t < 3; t++) {
      __NOP();
      if (get_singlegpio_in(GPIO_CHARGE_DETECT) == 0)     // check a second time, e.g. filter noise
      {
          gpio_charge_detect_complete_count += 1;
      }
  }

  if (gpio_charge_detect_complete_count < 3) {
      // GPIO4 输出低电平，开始 PWM 输出
      // Start charging
      set_singlegpio_out(0, GPIO_BUCK_EN);        // switch off buck
      sys_tim_pwm_start();                        // start PWM timer which controls the switch of the booster circuit

      while (1) {
        // 检测 GPIO3 是否低电平
        // Main charging loop
        // Just wait until the voltage detector switch triggers
        if (get_singlegpio_in(GPIO_CHARGE_DETECT) == 0)         // check charge
        {
            __NOP();

            if (get_singlegpio_in(GPIO_CHARGE_DETECT) == 0)     // check a second time, e.g. filter noise
            {
                break;  // cap almost full -> leave charging loop
            }
        }
      }

      // Continue charging for a given time
      // If the capacitor shall be charged to a higher value than the trip level of the voltage detector, just keep charging for a while.
      delay_ticks(ms2ticks(200));

      // 如果 GPIO3 输出低电平，停止 PWM
      // Stop charging
      sys_tim_pwm_stop();
  }

  // GPIO4 输出高电平
  // Switch on buck and wait until power is up
  // Booster and buck converter shall never be switched on at the same time. The input of the booster and the output of the buck converter share the same network.
  set_singlegpio_out(1, GPIO_BUCK_EN);        // switch on buck
  delay_ticks(ms2ticks(100));
}

void buck_complete() {
  // GPIO4 输出低电平
  set_singlegpio_out(0, GPIO_BUCK_EN);        // job done - switch off buck

  // 停止 PWM
  // disable booster: set GPIO to 0
  sys_tim_pwm_stop();
  set_singlegpio_out(0, GPIO_PWM);
  set_singlegpio_alt(GPIO_PWM, 0, 0);

  // disable pullup on charge detect
  single_gpio_iocfg(false, false, false, false, false, GPIO_CHARGE_DETECT);
}
