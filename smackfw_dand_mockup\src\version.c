/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file    version.c
 */

/*
==============================================================================
   1. INCLUDE FILES
==============================================================================
*/

#include "version.h"

/*
==============================================================================
   2. LOCAL DEFINITIONS
==============================================================================
*/

enum
{
    fw_application_dand_mockup = 0x000001,      // Dandelion mockup for Smack Showcase app
};

/*
==============================================================================
   4. LOCAL TYPES
==============================================================================
*/

/*
==============================================================================
   4. DATA
==============================================================================
*/

const Version_t version =
{
    .platform = (((uint8_t) DANDELION & 0x0F) << 4) | ((uint8_t) SMACK & 0x0F),
    .version  = ((FW_VERSION_MAJOR & 0x0F) << 4) | ((FW_VERSION_MINOR & 0x0F)),
    .step = (FW_VERSION_STEP & 0xFFFF),
    .commit_id = {0x00, 0x00, 0x00},
    .dirty = 0,
    .crc = 0x00000000
};


// Remark for linker script:
// The version struct above must be located at a well known, fixed address. This is achieved
// by defining a memory region and a segment in the linker script which defines this memory area.
// The segment lists one section which describes - depending on the compiler options - only the
// struct above or *all* variables in this file. In case the linker reports an overflow of the
// memory region, the latter options are active, and you have to adjust either the options, or
// the setup of this source file and the linker script (e.g. by specifying a special section for
// those variables which are intended to be located in a special section).

__attribute((section(".fw_version")))
const uint32_t fw_version = ((FIRMWARE_VERSION_MAJOR & 0xff) << 24) |
                            ((FIRMWARE_VERSION_MINOR & 0xff) << 16) |
                            ((FIRMWARE_VERSION_STEP  & 0xff) <<  8) |
                            ((FIRMWARE_VERSION_BUILD & 0xff) <<  0);

// Store the platform ID of version struct in the most significant byte, use lower three bytes for application ID.
// If desired, application ID may be split into fields, but the result shall be unique for a class of application.
const uint32_t fw_platform = ((DANDELION & 0x0F) << 28) | ((SMACK & 0x0F) << 24) |
                             (fw_application_dand_mockup & 0xffffff);


/*
==============================================================================
   5. LOCAL FUNCTION PROTOTYPES
==============================================================================
*/


/*
==============================================================================
  6. LOCAL FUNCTIONS
==============================================================================
*/


/*
==============================================================================
   7. EXPORTED FUNCTIONS
==============================================================================
*/

/* --- End of File ------------------------------------------------ */
