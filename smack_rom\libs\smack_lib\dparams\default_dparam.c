/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file    default_dparam.c
 * @brief   Placeholder for device specific dparams in NVM.
 */

/*
==============================================================================
   1. INCLUDE FILES
==============================================================================
*/

#include "cmsis_compiler.h"
#include "dparam.h"


/**
 * @defgroup group_dparam_variables DPARAM variables
 * @ingroup group_dparam_interface
 * @{
 */
/// @}

// Following default DPARAM structure must be placed at beginning of NVM memory
// linker script for details
// Initialization of NVM is done during ATE testing
Dparams_t const dparams __attribute__ ((section (".nvm.DPARAMS"))) =
{
    .sifo =                                                      /**< [0x27f:0x000] reserved for SIFO NVM test and config        */
    {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    },

    .tpe =                                                       /**< [0x2ff:0x280] reserved for TPE                             */
    {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    },

    .trim_main_osc =                                             /**< [0x301:0x300] (10 bit) trimming value for main oscillator  */
    {
        0x47, 0x01                                               /** 22.11MHz + 10*562kHz + 7 * 38.4kHz                          */
    },

    .trim_stb_osc  = 0x12,                                       /**< [0x302:0x302] (5 bit) trimming value for stb oscillator    */

    .trim_bias     = 0x00,                                       /**< [0x303:0x303] (4 bit) trimming value for bias currents     */

    .trim_ldo_vddd = 0x00,                                       /**< [0x304:0x304] (4 bit) trimming value for VDDD LDO          */

    .trim_ldo_nvm  = 0x00,                                       /**< [0x305:0x305] (4 bit) trimming value for NVM LDO           */

    .trim_ldo_sens = 0x00,                                       /**< [0x306:0x306] (5 bit) trimming value for SENS LDO          */

    .trim_prim_ldo = 0x00,                                       /**< [0x307:0x307] (4 bit) trimming value for PRIM LDO          */

    .trim_i2v      = 0x00,                                       /**< [0x308:0x308] (6 bit) trimming value for I2V               */

    .trim_nvm_ref  = 0x00,                                       /**< [0x309:0x309] (6 bit) trimming value for NVM REF           */

    .trim_rfu =                                                  /**< [0x30b:0x30a] trimming, reserved for future usage          */
    {
        0xff, 0xff
    },

    .cal_stb_osc =                                               /**< [0x30f:0x30c] 32768 (0x8000) oscillations per 1 s          */
    32768,

    .cal_main_osc =                                              /**< [0x313:0x310] 28000000 (0x01ab3f00) oscillations per 1 s   */
    28000000,

    .ate_nvm_write_flag =                                        /**< [0x317:0x314] NVM erased and programmed by ATE indicator   */
    0xabbaabba,                                                  /**                yes/true = 0xABBAABBA                        */

    .cal_adc_ref_voltage =                                       /**< [0x31b:0x318] calibration value of adc refrence voltage    */
    {
        0x08, 0x07, 0x00, 0x00                                   /**                0x000708 = 1800 mV                           */
    },

    .rfu0 =                                                      /**< [0x37f:0x31c] 100 bytes reserved for future usage          */
    {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff
    },

    .chip_uid =                                                  /**< [0x387:0x380] (56 bit) unique chip-id                      */
    {
        .uid = {
            0x05,                                                /** ISO IFX manufacturer ID                                     */
            0xc0,                                                /** Family Code Byte                                            */
            0xbe,                                                /** UID(4), YH(2), XH(2)                                        */
            0xef,                                                /** XL(8)                                                       */
            0xde,                                                /** YL(8)                                                       */
            0xad,                                                /** WAFCNT0                                                     */
            0x00,                                                /** WAFCNT1                                                     */
        },
        .rfu  = 0x00                                             /** reserved                                                    */
    },

    .jtag_id =                                                   /**< [0x38b:0x388] JTAG_ID = 0x00000222                         */
    0x10222083,                                                  /** JTAG_ID of Smack/NAC1080 (M2046-A000-11)                    */

    .rfu1 =                                                      /**< [0x39f:0x38c]20 bytes reserved for future usage           */
    {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff
    },
    .tag_type_2 =                    /**< [0x3a0:0x3ff] 96 Bytes Tag2 area          */
    {
        0x05, 0xc0, 0xbe, 0xef,      /**< BLOCK0 UID0,1,2,3,4 */
        0xde, 0xad, 0x00, 0x00,      /**< BLOCK1 UID5,6,7,0x00 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK2 Internal Lock  Byte0 and Byte1 are relevant 0xff means read only*/
        0xE1,                        /**< Capability Container CC_0 Magic Number fixed to 0xE1 for type 2 Tag */
        0x10,                        /**< Capability Container CC_1 Mapping version default 0x10 */
        0x07,                        /**< Capability Container CC_2 size 14 Blocks --> 56 Bytes / 8 = 7d */
        0x0f,                        /**< Capability Container CC_3 Access Conditions , 0x0f  for read only tag w/o security*/
        0x03, 0x2e, 0xd1, 0x01,      /**< BLOCK4  NDEF, Length, MB + ME + Well known, ID length */
        0x2a, 0x54, 0x02,  'e',      /**< BLOCK5  Payload lenth, ID, language length, lang0 */
        'n',   'I',  'n',  'f',      /**< BLOCK6  lang1, payload .... */
        'i',   'n',  'e',  'o',      /**< BLOCK7  */
        'n',   ' ',  'T',  'e',      /**< BLOCK8  */
        'c',   'h',  'n',  'o',      /**< BLOCK9  */
        'l',   'o',  'g',  'i',      /**< BLOCK10 */
        'e',   's',  ' ',  'A',      /**< BLOCK11 */
        'G',   ' ',  ' ',  'S',      /**< BLOCK12 */
        'm',   'A',  'c',  'K',      /**< BLOCK13 */
        ' ',   ' ',  'V',  '4',      /**< BLOCK14 */
        '.',   '0',  '.',  '1',      /**< BLOCK15 ... payload */
        0xfe, 0xff, 0xff, 0xff,      /**< BLOCK16 terminator */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK17 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK18 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK19 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK20 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK21 */
        0xff, 0xff, 0xff, 0xff,      /**< BLOCK22 */
        0xff, 0xff, 0xff, 0xff       /**< BLOCK23 not used */
    }

};


/* --- End of File ------------------------------------------------ */
