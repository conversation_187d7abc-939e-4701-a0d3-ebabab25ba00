/* ============================================================================
** Copyright (c) 2022 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     motor_stepwise_time.c
 *  @brief    Smack stepwise motor control, based on timing
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#if defined DEBUG && DEBUG
#warning generated HAL in use
#include "scus_hal.h"
#include "hbctrl_hal.h"
#endif
#include "rom_lib.h"

// Smack NVM lib
#include "system_lib.h"
#include "sys_tim_lib.h"
#include "shc_lib.h"

// Smack stepwise project
#include "settings.h"
#include "motor_common.h"
#include "motor_stepwise_time.h"

//#if defined DEBUG && DEBUG
#include "smack_dand_mockup.h"
//#endif


#if defined DEBUG && DEBUG
// ctrl: ls2 ls1 hs2 hs1
// stat: hs1 ls1 hs2 ls2
static void dbghb(uint32_t state, uint32_t volt, uint32_t ch)
{
    static const uint8_t ctrl2stat[16] =
    {
        0x0,
        0x8,
        0x2,
        0xa,
        0x4,
        0xc,
        0x6,
        0xe,
        0x1,
        0x9,
        0x3,
        0xb,
        0x5,
        0xd,
        0x7,
        0xf,
    };
    static struct
    {
        uint32_t state;
        uint32_t ch;
        uint32_t hbctrl;
        uint32_t hbstat;
    } data, last_data;
    static uint32_t last_volt;
    //uint32_t ctrl, stat;
    data.state = state;
    data.ch = ch;
    data.hbctrl = HBCTRL_HBCTRL_CTRL__GET();
    data.hbstat = HBCTRL_HBCTRL_STAT__GET();

    if (memcmp(&data, &last_data, sizeof(data)) != 0 || volt + 300 < last_volt || volt > last_volt + 300)
    {
        dbgp("s:%u hb:%x/%x c:%u(%u)\n", state, ctrl2stat[(data.hbctrl >> 1)] & 0xf, data.hbstat & 0xf, volt, ch);
        last_data = data;
        last_volt = volt;
    }
}
#else
#define dbghb(a,b,c)
#endif


// prototypes
static motor_rc_t motor_init(void);
static motor_rc_t motor_exit(void);
static motor_rc_t motor_task(void);
static motor_rc_t motor_control(smack_motor_command_t command);
static motor_rc_t motor_config(void);


// globals
smack_motor_control_status_t motor_stepwise_time_status;

const smack_motor_control_t motor_stepwise_time_control =
{
    .init = motor_init,
    .exit = motor_exit,
    .task = motor_task,
    .control = motor_control,
    .config = motor_config,
};


typedef enum motor_state_e
{
    s_lock_init = 0,
    //s_lock_idle,
    s_lock_charge,
    s_lock_open,
    s_lock_open_pause,
    s_lock_close,
    s_lock_close_pause,
} motor_state_t;

// local
static struct
{
    uint32_t lastTick;
    uint32_t t_total;   // total motor runtime
    uint32_t runtime_acc;    // accumulated runtime
    motor_state_t state;
    smack_motor_command_t command;
} motor_state;



static void hb_on(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(false, false, false, true);
    set_hb_switch(true, false, false, true);
}

static void hb_forward_off(void)
{
    set_hb_switch(true, false, false, false);
}

static void hb_reverse(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(false, false, true, false);
    set_hb_switch(false, true, true, false);
}

static void hb_reverse_pause(void)
{
    set_hb_switch(false, false, true, false);
}

static void hb_reverse_off(void)
{
    set_hb_switch(false, true, false, false);
    set_hb_switch(false, false, false, false);
    set_hb_switch(true, false, false, false);
}

static void hb_off(void)
{
    set_hb_switch(false, false, false, false);
    set_hb_switch(true, false, false, false);
}

static motor_rc_t motor_init(void)
{
    motor_config();
    set_hb_eventctrl(false);
    hb_off();
    //switch_on_sense();
    motor_state.state = s_lock_init;
    return 0;
}

static motor_rc_t motor_exit(void)
{
    set_hb_switch(false, false, false, false);
    vclamp_set(0);
    //switch_off_sense();         // may conflict with other measurements
    motor_state.state = s_lock_init;
    return 0;
}

static motor_rc_t motor_config(void)
{
    uint32_t bf;

    motor_state.t_total = ms2ticks(motor_stepwise_time_status.t_total);
    motor_stepwise_time_status.progress_threshold = motor_state.t_total;
    // as charge_threshold is not passed in status struct, calculate it from v_clamp, e.g. subtract tolerances of clamping and voltage comparison
    motor_stepwise_time_status.charge_threshold = motor_stepwise_time_status.v_clamp - (motor_stepwise_time_status.v_clamp / 16);

    // assume 0:3.0V, 1:3.3V, 2:3.6V
    switch (motor_stepwise_time_status.v_clamp)
    {
        case 3000:
            bf = 0;
            break;

        case 3300:
            bf = 1;
            break;

        case 3600:
            bf = 2;
            break;

        default:
            bf = 0xfffffff0;
    }

    if (bf < 3)
    {
        vclamp_set(bf);
    }

    return 0;
}

static motor_rc_t motor_control(smack_motor_command_t command)
{
    motor_state.command = command;
    return 0;
}

static motor_rc_t motor_task(void)
{
//    static uint32_t count;
    uint32_t now;
    bool close, open, stop, cmp;
    motor_rc_t rc = 0;
#if defined DEBUG && DEBUG
    motor_state_t last_state = motor_state.state;
#endif

    open = close = stop = false;
    __DMB();

    // should be enclosed in critical section
//    if (lock_control_stop /*|| (lock_control & ??)*/)
//    {
//        stop = true;
//    }
//    else
    if (motor_state.command == motor_ctrl_reverse)
    {
        close = true;
        dbgp("motor:close\n");
    }
    else if (motor_state.command == motor_ctrl_on)
    {
        open = true;
        dbgp("motor:open\n");
    }

    motor_state.command = 0;

// TODOs:
// - start lock operation only when cap is fully charged
// - add overall timeout
//    lock_control_lock = lock_control_unlock = lock_control_stop = false;
    //lock.control &= ~(lock_control_locked | lock_control_unlocked);
    __DMB();

    switch (motor_state.state)
    {
        case s_lock_charge:
            sense_lock();
            cmp = shc_compare(shc_channel_ma, motor_stepwise_time_status.charge);
            sense_free();

            if (cmp)
            {
                motor_stepwise_time_status.charge += 50;
            }
            else if (motor_stepwise_time_status.charge > 50)
            {
                motor_stepwise_time_status.charge -= 50;
            }
            else
            {
                motor_stepwise_time_status.charge = 0;
            }

            dbghb(motor_state.state, motor_stepwise_time_status.charge, shc_channel_ma);

            if (motor_stepwise_time_status.charge >= motor_stepwise_time_status.charge_threshold)
            {
                if (open)
                {
//                count = 0;
                    motor_stepwise_time_status.status = motor_status_moving;
                    motor_stepwise_time_status.progress = 0;
                    motor_state.runtime_acc = 0;
//                motor_status_set(lock_control_operating);
                    hb_on();
                    motor_state.state = s_lock_open;
                    //log_write(lock_control_unlocked);
                    motor_state.lastTick = smack_motor_services.timer_get();
                }
                else if (close)
                {
//                count = 0;
                    motor_stepwise_time_status.status = motor_status_moving;
                    motor_stepwise_time_status.progress = 0;
                    motor_state.runtime_acc = 0;
//                motor_status_set(lock_control_operating);
                    hb_reverse();
                    motor_state.state = s_lock_close;
                    //log_write(lock_control_locked);
                    motor_state.lastTick = smack_motor_services.timer_get();
                }
            }

            break;

        case s_lock_open:
            dbghb(motor_state.state, motor_stepwise_time_status.charge, shc_channel_ma);
            now = smack_motor_services.timer_get();
            motor_stepwise_time_status.progress = now - motor_state.lastTick + motor_state.runtime_acc;

            if (motor_stepwise_time_status.progress >= motor_stepwise_time_status.progress_threshold)
            {
                hb_forward_off();
                motor_stepwise_time_status.status = motor_status_pos1;
//                motor_status_set(lock_control_unlocked);
                motor_state.state = s_lock_charge;
            }
            else if (now - motor_state.lastTick >= motor_stepwise_time_status.param1)   // t_on passed?
            {
                hb_forward_off();
                motor_state.runtime_acc += now - motor_state.lastTick;
                motor_state.lastTick = now;
                motor_state.state = s_lock_open_pause;
            }

            break;

        case s_lock_open_pause:
            now = smack_motor_services.timer_get();

            if (now - motor_state.lastTick >= motor_stepwise_time_status.param2)        // t_off passed?
            {
                hb_on();
                motor_state.lastTick = smack_motor_services.timer_get();
                motor_state.state = s_lock_open;
            }

            break;

        case s_lock_close:
            dbghb(motor_state.state, motor_stepwise_time_status.charge, shc_channel_mb);
            now = smack_motor_services.timer_get();
            motor_stepwise_time_status.progress = now - motor_state.lastTick + motor_state.runtime_acc;

            if (motor_stepwise_time_status.progress >= motor_stepwise_time_status.progress_threshold)
            {
                hb_reverse_off();
                motor_stepwise_time_status.status = motor_status_pos2;
//                motor_status_set(lock_control_unlocked);
                motor_state.state = s_lock_charge;
            }
            else if (now - motor_state.lastTick >= motor_stepwise_time_status.param1)   // t_on passed?
            {
                hb_reverse_pause();
                motor_state.runtime_acc += now - motor_state.lastTick;
                motor_state.lastTick = now;
                motor_state.state = s_lock_close_pause;
            }

            break;

        case s_lock_close_pause:
            now = smack_motor_services.timer_get();

            if (now - motor_state.lastTick >= motor_stepwise_time_status.param2)        // t_off passed?
            {
                hb_reverse();
                motor_state.lastTick = smack_motor_services.timer_get();
                motor_state.state = s_lock_close;
            }

            break;

        case s_lock_init:
        default:
            motor_state.state = s_lock_charge;
    }

#if defined DEBUG && DEBUG

    if (last_state != motor_state.state)
    {
        printf("motor state:%u->%u charge:%u progress:%u\n", last_state, motor_state.state, motor_stepwise_time_status.charge, motor_stepwise_time_status.progress);
    }

#endif
    return rc;
}
