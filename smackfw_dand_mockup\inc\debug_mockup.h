/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     debug_mockup.h
 *
 * @brief    Smack dandelion test example file.
 *
 * @version  v1.0
 * @date     2020-05-20
 *
 * @note
 */

/* ============================================================================
** Copyright (C) 2020 Infineon. All rights reserved.
**               Infineon Technologies, PSS ACDC / DES ACDC
** ============================================================================
**
** ============================================================================
** This document contains proprietary information. Passing on and
** copying of this document, and communication of its contents is not
** permitted without prior written authorisation.
** ============================================================================
*
*/
/* lint -save -e960 */

#ifndef _DEBUG_MOCKUP_H_
#define _DEBUG_MOCKUP_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup debug_mockup
 * @{
 */


/** @addtogroup fw_config
 * @{
 */

#include "uart_drv.h"
#include "uart_lib.h"

#include "printf.h"
#define dbgp(...) printf(__VA_ARGS__)

extern void debug_init(void);
extern void debug_task(void);
extern void debug_log_de(bool tx, uint16_t datapoint, uint16_t index);


/** @} */ /* End of group fw_config */


/** @} */ /* End of group debug_mockup */

/** @} */ /* End of group Infineon */

#endif /* _DEBUG_MOCKUP_H_ */
