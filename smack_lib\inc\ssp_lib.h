/* ============================================================================
** Copyright (c) 2022 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     ssp_lib.h
 *
 * @brief    This module contains a driver for the SSP module.
 *
 *           Currently, this driver only supports SPI master mode.
 *
 *           The /CS signal (chip select, also called slave select) can be generated by the SSP module itself, e.g. hardware controlled, or can be emulated in software.
 *           If the clock polarity is configured to use the first edge as sample clock (SPH=0), the SSP will release the /CS signal after each symbol for one clock, then assert it again.
 *           This is not compatible for many SPI slaves which use the /CS signal to start a frame which has a command or an address in the first symbol, followed by payload data. Due to
 *           the pulse on the /CS line, they will recognize every single symbol of the SPI transfer as the command or address field of a frame, but never as the payload. In this case,
 *           configure the driver to use software emulated /CS generation which asserts the /CS signal when ssp_transfer() is called, and releases it when the transfer was completed.
 *
 * @version  v1.0
 * @date     2021-08-01
 *
 */

#ifndef SSP_LIB_H_
#define SSP_LIB_H_

/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack
 * @{
 */


/** @addtogroup ssp_lib
 * @{
 */


/* *
 * @brief  SSP driver for Smack
 */


#include <stdbool.h>


#ifdef __cplusplus
extern "C" {
#endif


/**
 * @brief  Select master/slave operation
 *
 *         Note: Currently, only master mode is supported. Master and slave mode cannot be changed on the fly.
 */
typedef enum ssp_ifmode_lib_e
{
    ssp_master_lib = 0, //!< interface master
    ssp_slave_lib = 1   //!< interface slave
} ssp_master_slave_lib_t;

/**
 * @brief  Select clock phase (SPH)
 */
typedef enum ssp_phase_lib_e
{
    ssp_sample = 0,     //!< data is sampled on leading clock edge, and shifted on trailing edge
    ssp_shift = 1       //!< data is shifted on leading clock edge, and sampled on trailing edge
} ssp_phase_lib_t;

/**
 * @brief  Select clock polarity (SPO)
 */
typedef enum ssp_polarity_lib_e
{
    ssp_clock_low = 0,  //!< SSPCLKOUT pin of SPI is LOW in idle state
    ssp_clock_high = 1  //!< SSPCLKOUT pin of SPI is HIGH in idle state
} ssp_polarity_lib_t;

/**
 * @brief  SSP driver error codes
 */
typedef enum ssp_error_e
{
    SSP_OK           = 0,       //!< no error
    SSP_ERROR        = -1,      //!< error, not further specified
    SSP_ERROR_BUSY   = -2,      //!< SSP is busy with a transfer
    SSP_ERROR_RX_OVR = -3,      //!< Rx overrun, data lost
} ssp_error_t;

/**
 * @brief  SSP driver configuration
 */
typedef struct ssp_config_s
{
    uint32_t baudrate;          //!< SPI Clock Out/In frequency
    uint8_t bits;               //!< number of bits per symbol (4 through 16)
    ssp_polarity_lib_t polarity; //!< SPI clock polarity (SPO)
    ssp_phase_lib_t phase;      //!< SPI clock phase (SPH)
    ssp_master_slave_lib_t master_slave;    //!< master or slave role of this node (currently only master mode is supported)
    int8_t gpio_cs;             //!< GPIO pin for software controlled /CS; set to -1 to use HW controlled /CS
} ssp_config_t;


/**
 * @brief  Initialize the SSP module, e.g. setup internal data structures
 * @param  irq  IRQ number when configuring IRQ matrix
 */
extern void ssp_init(uint8_t irq);

/**
 * @brief  Open the SSP module for data transfers, e.g. start clock, activate configuration.
 */
extern int8_t ssp_open(void);

/**
 * @brief  Stop the SSP module, e.g. switch off clock.
 */
extern void ssp_close(void);

/**
 * @brief  Set the configuration of the SSP module.
 * @param  config  pointer to struct holding configuration
 */
extern void ssp_config_set(const ssp_config_t* config);


/**
 * @brief  Configure standard GPIO pins for use with SSP module.
 *         For each of the signals that shall be configured for use with the SSP module, this functions sets up the GPIO mux and the output or input drivers.
 *         Note: If desired, each of the GPIO pins can be configured manually with the functions provided by the GPIO driver.
 * @param  tx     request to configure local Tx pin for output (e.g. MOSI in master operation)
 * @param  rx     request to configure local Rx pin for input (e.g. MISO in master operation)
 * @param  clock  request to configure local SPI clock pin (output in master operation)
 * @param  fs     request to configure local SPI /CS pin (output in master operation; note: on SSP, this signal is called "Frame Sync")
 */
extern void ssp_config_gpio(bool tx, bool rx, bool clock, bool fs /* cs? */);

/**
 * @brief  Start an SPI transfer.
 *         The function sets up an SPI transfer job (transmitting and receiving) and returns. The transfer is performed in background.
 *         If the width of a symbol is more than 8 bits, the data is read from and written to the buffers as uint16_t. Make sure that the buffer sizes are sufficant to hold the data.
 *         Note: To wait for the transfer to complete use ssp_wait().
 * @param  tx_buffer  pointer to the data that shall be  transmitted. If Tx data is don't care, this pointer can be given as NULL, and the SSP will send all zeroes.
 * @param  rx_buffer  pointer to a buffer where the received data will be stored. If Rx data is don't care, this pointer can be given as NULL, and the SSP will discard the Rx data.
 * @param  count      number of symbols that shall be transferred.
 * @return error code, 0 on success
 */
extern int32_t ssp_transfer(const uint8_t* tx_buffer, uint8_t* rx_buffer, uint32_t count);

/**
 * @brief  Wait for the current transfer to complete.
 */
extern void ssp_wait(void);

/**
 * @brief  Read the status of the last transfer.
 * @return error code (<0) on error or when busy, transferred symbols (>=0) on success
 */
extern int32_t ssp_error(void);

/**
 * @brief  Read the peripheral ID from the SSP.
 * @return peripheral ID
 */
extern uint32_t ssp_get_peripheral_id(void);

/**
 * @brief  Read the primecell ID from the SSP.
 * @return orimecell ID
 */
extern uint32_t ssp_get_primecell_id(void);

/**
 * @brief  Interrupt service routine.
 *         This ISR must be configured in the ssp_hand_addr field in APARAM, and the SSP interrupt must be selected in the proper matrix entry (hp_irq9_cfg through hp_irq14_cfg) for the interrupt used.
 */
extern void ssp_isr(void);


#ifdef __cplusplus
}
#endif

/** @} */ /* End of group ssp_lib */

/** @} */ /* End of group Smack */

/** @} */ /* End of group Infineon */

#endif /* SSP_LIB_H_ */
