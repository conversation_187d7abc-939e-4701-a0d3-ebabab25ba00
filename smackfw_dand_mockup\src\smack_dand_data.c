/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     smack_dand_data.c
 *  @brief    Smack dandelion mockup: global data declarations.
 *
 *  This example shall provide various functionalities to support development of a smartphone app, e.g.
 *  optical feedback of connection status. In further versions, it shall support development of an SDK
 *  on the smartphone as well as on the Smack which provides functionalities to build real world devices
 *  and apps.
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include <stddef.h>
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#include "rom_lib.h"
//
//// Smack NVM lib
#include "inet_lib.h"

// Smack stepwise project
#include "version.h"
#include "settings.h"
#include "smack_dand_mockup.h"
#include "smack_dand_data.h"



// get some ROM insights
//#define currstate (*(NFC_State_enum_t*)0x02014e)
//#define mailbox (*(Mailbox_t*)0x02000c)

//#define MAILBOX_APP     (MAILBOX_SIZE * 2U / 4U)        // mailbox area used as scratchpad for reader app
//#define MAILBOX_FW      (MAILBOX_SIZE * 3U / 4U)        // mailbox area used as scratchpad for firmware (e.g. write some known values to be read by reader app)

//#undef wait_about_1ms
//#define wait_about_1ms  (XTAL / 1000U)          // calculated from oscillator; more exact than rough estimate
//#define ms2ticks(ms)    ((ms) * wait_about_1ms)

/** The functions which drive the motor are using timers in a different manner to estimate if the operationis completed.
 *  The timer channels to be used are defined here.
 */
//#define TIMER_SINGLE    1       // timer # used for single shot delays
//#define TIMER_CLOCK     5       // timer # configured together with the preceeding timer as a free running 32 bit timer to be used for timing measurements

// interrupt number used in system timer calls
//#define SYSTIM_IRQ      9


//-------------------------------------------------------------
// globals/statics

static uint32_t scratch32;
static uint16_t scratch16;
uint8_t scratch8;

// some of the data points to be exchanged with NFC reader

static const char fw_name[] = "NFC Lock";


//-------------------------------------------------------------


smartlock_user_t smartlock_user;
smartlock_config_t config;      // live instance in RAM
smartlock_data_t lock =
{
    .charge_raw_threshold = VCCHB_CLAMP_RAW, // raw value for "full"
};
session_t session;
log_interface_t log_if;


//-------------------------------------------------------------
// prototypes


//-------------------------------------------------------------
// data point list

#if 0   // decryption override disabled now, to enable use this if clause: #if (FIRMWARE_VERSION_BUILD & 1) == 0
# warning datapoints: encryption optional
# define DE_CRYPT        (0)
# define N_DE_CRYPT      (0)
#else
# warning datapoints: encryption mandatory
# define DE_CRYPT        (data_point_encrypt)
# define N_DE_CRYPT      (0)
#endif

const data_point_entry_t data_point_list[] =
{
    // id                       type                                             length             element                        notify
    // status
    {X0000_SMACK_FLAGS,         data_point_uint32,                               sizeof(uint32_t),  &session.flags,                data_rx, data_tx},
    {X0001_FW_VERSION,          data_point_uint32,                               sizeof(uint32_t),  (uint32_t*) &fw_version,       data_rx, data_tx},
    {X0002_FW_PLATFORM,         data_point_uint32,                               sizeof(uint32_t),  (uint32_t*) &fw_platform,      data_rx, data_tx},
    {X0003_FW_NAME,             data_point_string,                               sizeof(fw_name),   (char*)fw_name,                data_rx, data_tx},
    {X0004_UID,                 data_point_uint64,                               sizeof(uint64_t),  (uint64_t*) &session.uid,      data_rx, data_tx},
    {X0005_LOCK_ID,             data_point_uint64,                               sizeof(uint64_t),  (uint64_t*) &lock.id,          data_rx, data_tx},
    {X0010_CHARGE_RAW,          data_point_uint16                    | DE_CRYPT, sizeof(uint16_t),  &lock.charge_raw,              data_rx, data_tx},
    {X0011_CHARGE_RAW_THRESHOLD, data_point_uint16                   | DE_CRYPT, sizeof(uint16_t),  &lock.charge_raw_threshold,    data_rx, data_tx},
    {X0012_CHARGE_PERCENT,      data_point_uint8                     | DE_CRYPT, sizeof(uint8_t),   &lock.charge_percent,          data_rx, data_tx},
    {X0020_LOCK_CONTROL_STATUS, data_point_uint8                     | DE_CRYPT, sizeof(uint8_t),   &lock.control_status,          data_rx, data_tx},
    {X0021_LOCK_CONTROL_PROGRESS, data_point_uint8                   | DE_CRYPT, sizeof(uint8_t),   &lock.control_progress,        data_rx, data_tx},
    {X0030_USER_COUNT,          data_point_uint8,                                sizeof(uint8_t),   &session.user_count,           data_rx, data_tx},
#ifndef SMACK_DATAPOINT_FULL
    {X00F0_RF_STRENGTH_RAW,     data_point_uint16                    | DE_CRYPT, sizeof(uint16_t),  &session.rf_strength_raw,      data_rx, data_tx},

    // temperature is read from int32 as int16, will work on little endian platforms
    {X0080_TEMPERATURE,          data_point_int16,                               sizeof(uint16_t),  &session.temperature,         data_rx, data_tx},
    {X0081_HUMIDITY,             data_point_int16,                               sizeof(uint16_t),  &session.humidity,            data_rx, data_tx},
    {X0082_PRESSURE,             data_point_int16,                               sizeof(uint16_t),  &session.pressure,            data_rx, data_tx},
    {X0083_RFU,                  data_point_int32,                               sizeof(uint32_t),  &session.measurement_rfu,     data_rx, data_tx},
#endif

    // control
    {X0120_LOCK_CONTROL,        data_point_uint8  | data_point_write | DE_CRYPT, sizeof(uint8_t),   &lock.control,                 data_rx, data_tx},
    {X0121_LOCK_ARM,            data_point_uint8  | data_point_write | DE_CRYPT, sizeof(uint8_t),   &lock.arm,                     data_rx, data_tx},
    // control alternative
//    { 0x130, data_point_bool   | data_point_write | DE_CRYPT, sizeof(uint8_t),   &lock_control_lock,     NULL},
//    { 0x131, data_point_bool   | data_point_write | DE_CRYPT, sizeof(uint8_t),   &lock_control_unlock,   NULL},
//    { 0x132, data_point_bool   | data_point_write | DE_CRYPT, sizeof(uint8_t),   &lock_control_stop,     NULL},

    // configuration
    {X1000_CONFIG_METHOD,       data_point_uint8  | data_point_write | DE_CRYPT, sizeof(uint8_t),   &config.method,                data_rx, data_tx},
    {X1001_CONFIG_VCLAMP,       data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.v_clamp,               data_rx, data_tx},
    {X1002_CONFIG_VOLT_VSTART,  data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.volt.v_start,          data_rx, data_tx},
    {X1003_CONFIG_VOLT_VSTOP,   data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.volt.v_stop,           data_rx, data_tx},
    {X1004_CONFIG_TIME_TON,     data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.time.t_on,             data_rx, data_tx},
    {X1005_CONFIG_TIME_TOFF,    data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.time.t_off,            data_rx, data_tx},
    {X1006_CONFIG_SINGLE_VSTART, data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t), &config.single.v_start,        data_rx, data_tx},
    {X1007_CONFIG_TIME_TOTAL,   data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &config.t_total,               data_rx, data_tx},

    // process data
    {X1800_DATE,                data_point_int64  | data_point_write,            sizeof(int64_t),   &session.date,                 data_rx, data_tx},
    {X1801_USERNAME,            data_point_string | data_point_write,            sizeof(session.username) - 1, &session.username,  data_rx, data_tx},     // max. 40 chars
    {X1810_LOG_COUNT,           data_point_uint16                    | DE_CRYPT, sizeof(uint16_t),  &log_if.count,                 data_rx, data_tx},
    {X1811_LOG_SELECT,          data_point_uint16 | data_point_write | DE_CRYPT, sizeof(uint16_t),  &log_if.select,                data_rx, data_tx},
    {X1812_LOG_STATUS,          data_point_uint32                    | DE_CRYPT, sizeof(uint32_t),  &log_if.status,                data_rx, data_tx},
    {X1813_LOG_DATE,            data_point_int64                     | DE_CRYPT, sizeof(int64_t),   &log_if.date,                  data_rx, data_tx},
    {X1814_LOG_USER,            data_point_string                    | DE_CRYPT, sizeof(log_if.username) - 1,  &log_if.username,   data_rx, data_tx},

    // authentication
    {X1900_USER_SELECT,         data_point_uint8  | data_point_write,            sizeof(uint8_t),   &session.user_select,          data_rx, data_tx},
    {X1901_LOCK_KEY_STORE,      data_point_uint32 | data_point_write | DE_CRYPT, sizeof(uint32_t),  &session.lock_key_store,       data_rx, data_tx},
    {X1902_LOCK_KEY,            data_point_array  | data_point_write | DE_CRYPT, sizeof(session.lock_key),  &session.lock_key,     data_rx, data_tx},
    {X1903_LOCK_KEY_CHECK,      data_point_array                     | DE_CRYPT, sizeof(session.lock_key_check), &session.lock_key_check, data_rx, data_tx},

#ifndef SMACK_DATAPOINT_FULL
    // test/debug
    {XE100_GLOBAL_COUNTER,      data_point_uint32,                    sizeof(uint32_t),  &session.global_counter,       data_rx, data_tx},
    {XE102_TEMPERATURE,         data_point_int32,                     sizeof(int32_t),   &session.temperature,          data_rx, data_tx},
    {XF000_SCRATCH32,           data_point_uint32 | data_point_write, sizeof(scratch32), &scratch32,                    data_rx, data_tx},
    {XF001_SCRATCH16,           data_point_uint16 | data_point_write, sizeof(scratch16), &scratch16,                    data_rx, data_tx},
    {XF002_SCRATCH8,            data_point_uint8  | data_point_write, sizeof(scratch8),  &scratch8,                     data_rx, data_tx},
#endif
};
const uint16_t data_point_count = (sizeof(data_point_list) / sizeof(data_point_list[0]));


__attribute__((section(".nvm_const")))
const config_device_t config_device =
{
    .lock_id = 0x0123456789abcdefULL,
    //.su_key = {{0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff}},
    .su_key = {{'a', '0', '6', '3', 'b', '9', '6', '5', 'e', '2', '6', '3', 'e', '1', '4', 'd'}},
};


//-------------------------------------------------------------

// Variables should be initialized by zero_table, but sometimes this option is disabled. Then, this function may be used instead.
void vars_init(void)
{
    union
    {
        uint64_t ll;
        uint32_t l[2];
    } tmp;
    const Dparams_t* dparams;

    memset(&session, 0, sizeof(session));
    //session.flags = 0;
    //session.global_counter = 0;
    lock.control_status = 0;
    lock.control_progress = 0;
    lock.charge_raw = 0;
    lock.charge_raw_threshold = VCCHB_CLAMP_RAW; // raw value for "full"
    lock.charge_percent = 0;
    lock.control = 0;

    dparams = dparam_pointer_get();
    tmp.ll = ntohll(*(uint64_t*)&dparams->chip_uid);
    session.uid = tmp.ll;
    tmp.ll = (tmp.ll >> 4) | (0x5000000000000005ULL);
    // tmp.l[0] = ntohl(tmp.l[0]);
    // tmp.l[1] = ntohl(tmp.l[1]);
    lock.id = tmp.ll;

    if ((*(uint64_t*)&dparams->chip_uid) == 0 || (*(uint64_t*)&dparams->chip_uid) == 0xffffffffffffffffULL)
    {
        lock.id = config_device.lock_id;
    }
}
