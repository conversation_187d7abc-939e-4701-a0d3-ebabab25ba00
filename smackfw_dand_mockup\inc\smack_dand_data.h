/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_dand_data.h
 *
 * @brief    Smack dandelion mockup: global data declarations.
 *
 * @version  v1.0
 * @date     2020-05-20
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_DAND_DATA_H_
#define _SMACK_DAND_DATA_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack_dand_data
 * @{
 */


/** @addtogroup fw_config
 * @{
 */

#include "aes_lib.h"
#include "smack_exchange.h"


#define X0000_SMACK_FLAGS               (0)
#define X0001_FW_VERSION                (0x01)
#define X0002_FW_PLATFORM               (0x02)
#define X0003_FW_NAME                   (0x03)
#define X0004_UID                       (0x04)
#define X0005_LOCK_ID                   (0x05)
#define X0010_CHARGE_RAW                (0x10)
#define X0011_CHARGE_RAW_THRESHOLD      (0x11)
#define X0012_CHARGE_PERCENT            (0x12)
#define X0020_LOCK_CONTROL_STATUS       (0x20)
#define X0021_LOCK_CONTROL_PROGRESS     (0x21)
#define X0030_USER_COUNT                (0x30)

#define X0080_TEMPERATURE               (0x80)
#define X0081_HUMIDITY                  (0x81)
#define X0082_PRESSURE                  (0x82)
#define X0083_RFU                       (0x83)

#define X00F0_RF_STRENGTH_RAW           (0xf0)

// control
#define X0120_LOCK_CONTROL              (0x0120)
#define X0121_LOCK_ARM                  (0x0121)

// configuration
#define X1000_CONFIG_METHOD             (0x1000)
#define X1001_CONFIG_VCLAMP             (0x1001)
#define X1002_CONFIG_VOLT_VSTART        (0x1002)
#define X1003_CONFIG_VOLT_VSTOP         (0x1003)
#define X1004_CONFIG_TIME_TON           (0x1004)
#define X1005_CONFIG_TIME_TOFF          (0x1005)
#define X1006_CONFIG_SINGLE_VSTART      (0x1006)
#define X1007_CONFIG_TIME_TOTAL         (0x1007)

// process data
#define X1800_DATE                      (0x1800)
#define X1801_USERNAME                  (0x1801)     // max. 40 chars

#define X1810_LOG_COUNT                 (0x1810)
#define X1811_LOG_SELECT                (0x1811)
#define X1812_LOG_STATUS                (0x1812)
#define X1813_LOG_DATE                  (0x1813)
#define X1814_LOG_USER                  (0x1814)

// authentication
#define X1900_USER_SELECT               (0x1900)
#define X1901_LOCK_KEY_STORE            (0x1901)
#define X1902_LOCK_KEY                  (0x1902)
#define X1903_LOCK_KEY_CHECK            (0x1903)

// test/debug
#define XE100_GLOBAL_COUNTER            (0xe100)
#define XE102_TEMPERATURE               (0xe102)
#define XF000_SCRATCH32                 (0xf000)
#define XF001_SCRATCH16                 (0xf001)
#define XF002_SCRATCH8                  (0xf002)


#define LOCK_KEY_WRITE_COMMAND          (0x82d112e6)    // command pattern to store a lock key in NVM


extern const data_point_entry_t data_point_list[];
extern const uint16_t data_point_count;


// status (Smack -> reader)
enum
{
    smack_flags_error   = (1U << 31),
    smack_flags_locked  = (1U << 1),
    smack_flags_unlocked = (1U << 0)
};
#define SMACK_FLAGS_MOTOR_MASK ((uint32_t)(smack_flags_locked | smack_flags_unlocked))
//static uint32_t smack_flags;            // global flags, see enum above

enum
{
    lock_control_error  =    (1U << 7),
    lock_control_operating = (1U << 2),
    lock_control_locked =    (1U << 1),
    lock_control_unlocked =  (1U << 0)
};
#define SMACK_STATUS_MOTOR_MASK ((uint32_t)(lock_control_operating | lock_control_locked | lock_control_unlocked))


/**
 * @brief Struct holding the part of the smartlock configuration which shall be
 *        stored in NVM.
 */
typedef struct smartlock_config_s
{
    struct
    {
        uint16_t v_start;       //!< upper threshold [mV]
        uint16_t v_stop;        //!< lower threshold [mV]
    } volt;                     //!< voltage controlled stepwise motor movement
    struct
    {
        uint16_t t_on;          //!< motor on time [ms]
        uint16_t t_off;         //!< recharge pause [ms]
    } time;                     //!< timing controlled stepwise motor movement
    struct
    {
        uint16_t v_start;       //!< charging voltage to start motor [mV]
    } single;                   //!< single shot motor movement
    uint16_t v_clamp;           //!< VCCHB clamping voltage (tbd: mV or enum?)
    uint16_t t_total;           //!< total motor runtime [ms]
    uint8_t method;             //!< selected method (enum: stepwise/voltage, stepwise/timer, single shot)
} smartlock_config_t;
extern smartlock_config_t config;

typedef struct smartlock_data_s
{
    uint64_t id;

    uint16_t charge_raw;             // raw value of charging state
    uint16_t charge_raw_threshold; // raw value for "full"
    uint8_t charge_percent;          // charging state calculated as percent

    // control (reader -> Smack), control status (Smack -> reader)
    uint8_t control;            // lock control, uses bit positions defined for lock_control_status (supported: lock/unlock)
    uint8_t arm;
    //bool lock_control_lock;          // lock control as set of bool
    //bool lock_control_unlock;
    //bool lock_control_stop;
    uint8_t control_status;     // flags (bit field), see enum above
    uint8_t control_progress;   // progress of motor movement (updated after motor operation has started, e.g. when flag lock_control_operating was set)
} smartlock_data_t;
extern smartlock_data_t lock;

// user info; for now: lock key
typedef struct smartlock_user_s
{
    aes_block_t key;
    aes_block_t key2;
} smartlock_user_t;
extern smartlock_user_t smartlock_user;

typedef struct session_s
{
    uint64_t uid;
    int64_t date;               // date/time sent by smartphone (UNIX time, used in logs)

    aes_block_t lock_key;
    aes_block_t lock_key_check;
    uint32_t lock_key_store;    // datapoint to receive store command
    bool lock_key_received;     // a key was received and may be stored to NVM
    bool lock_key_write;        // write the key to NVM
    bool config_write;          // config was changed
    bool config_write_force;    // write config now, do not wait for further changes
    bool config_time_set;       // timestamp of change request set -> do not overwrite
    bool config_activate;       // config was changed -> put into effect
    uint32_t config_time;       // timestamp of first config request
    uint32_t config_activate_time; // timestamp when activate was triggered (last event)
    uint8_t method;             // current motor control method
    uint8_t user_count;
    uint8_t user_select;
    uint8_t user_selected;
    uint16_t user_select_count; // how often did we receive user_select?
    char username[40 + 1];

    uint32_t flags;            // global flags, see enum above
    uint16_t rf_strength_raw;

    // debug:
    int32_t temperature;             // chip temperature (uncalibrated), unit 0.1 degrees Celsius
    int16_t humidity;                // humidity, unit 0.1 percent
    int16_t pressure;                // pressure, unit 0.001 bar (for tyre pressure, e.g. 1 mbar for air pressure)
    int32_t measurement_rfu;         // dummy for future use
    uint32_t global_counter;         // global loop counter, incremented about every 100ms
    uint8_t erase;                   // clear some of the data arrays in NVM
} session_t;
extern session_t session;

typedef struct log_interface_s
{
    uint16_t count;
    uint16_t select;
    uint32_t status;
    int64_t date;
    char username[40 + 1];
} log_interface_t;
extern log_interface_t log_if;

typedef struct log_nvm_entry_s
{
    int64_t date;
    uint32_t status;
    char username[40 + 1];
} log_nvm_entry_t;
// size: 8 + 4 + 41 = 53 bytes


typedef struct config_device_s
{
    uint64_t lock_id;
    uint32_t rfu[2];
    aes_block_t su_key;
} config_device_t;
extern const config_device_t config_device;

// debug:
extern uint8_t scratch8;


// Prototypes

extern void vars_init(void);


/** @} */ /* End of group fw_config */


/** @} */ /* End of group Smack_dand_data */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_DAND_DATA_H_ */
