/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_dand_nvm_data.h
 *
 * @brief    Smack dandelion test example file - NVM storage.
 *
 * @version  v1.0
 * @date     2021-10-01
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_DAND_NVM_DATA_H_
#define _SMACK_DAND_NVM_DATA_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack_dand_nvm
 * @{
 */


/* * @addtogroup fw_config
 * @{
 */


#ifndef NVM_PAGE_SIZE   /* This macro may be defined in nvm.h in the future */
#define NVM_PAGE_SIZE (NVM_SIZE / NUMBER_OF_SECTORS / N_LOG_PAGES)    // should give page size of 128 bytes
#endif


// storage for persistent part of configuration in NVM
typedef union
{
    uint8_t b[NVM_PAGE_SIZE];
    struct
    {
        smartlock_config_t config;
        uint8_t magic;
        uint8_t seq;
    };
} config_nvm_t;


// storage for user configuration (e.g. credentials) in NVM
typedef union
{
    uint8_t b[NVM_PAGE_SIZE];
    struct
    {
        smartlock_user_t cfg;
        uint8_t magic;
        uint8_t seq;
    };
} user_cfg_nvm_t;

// storage for persistent part of log entries in NVM
typedef union
{
    uint8_t b[NVM_PAGE_SIZE];
    struct
    {
        log_nvm_entry_t log;
        uint8_t magic;
        uint8_t seq;
    };
} log_nvm_t;


#define CONFIG_MAGIC            (0x51)
#define USER_CFG_MAGIC          (0x53)
#define LOG_MAGIC               (0x52)


#define CONFIG_NVM_COUNT        2
#define USER_CFG_NVM_COUNT      1       //16
#define LOG_NVM_COUNT           16

extern const config_nvm_t config_nvm[CONFIG_NVM_COUNT];
extern const user_cfg_nvm_t user_cfg_nvm[USER_CFG_NVM_COUNT];
extern const log_nvm_t log_nvm[LOG_NVM_COUNT];


/* * @} */ /* End of group fw_config */


/** @} */ /* End of group Smack_dand_nvm */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_DAND_NVM_DATA_H_ */
